import type { BaseEntity, PageQuery } from '#/api/common';

export interface SupplierVO {
  /**
   * 主键ID
   */
  supplierId: number | string;

  /**
   * 供应商编码（规则：SUP+年+6位序列）
   */
  supplierCode: string;

  /**
   * 公司名称
   */
  name: string;

  /**
   * 公司类型
   */
  companyType: string;

  /**
   * 统一社会信用代码
   */
  creditCode: string;

  /**
   * 银行账户
   */
  bankAccount: string;

  /**
   * 开户行全称
   */
  bankName: string;

  /**
   * 固定电话
   */
  companyPhone: string;

  /**
   * 地址
   */
  companyAddress: string;

  /**
   * 官方邮箱
   */
  officialEmail: string;

  /**
   * 联系人列表
   */
  contacts?: SupplierContactVO[];

  /**
   * 企业资质列表
   */
  qualifications?: SupplierQualificationVO[];
}

export interface SupplierContactVO {
  /**
   * 联系人ID
   */
  contactId?: number | string;

  /**
   * 供应商ID
   */
  supplierId?: number | string;

  /**
   * 姓名
   */
  name: string;

  /**
   * 职位
   */
  position: string;

  /**
   * 手机号
   */
  phone: string;

  /**
   * 邮箱
   */
  email: string;

  /**
   * 微信
   */
  wechat?: string;

  /**
   * QQ
   */
  qq?: string;
}

export interface SupplierQualificationVO {
  /**
   * 资质ID
   */
  qualificationId?: number | string;

  /**
   * 供应商ID
   */
  supplierId?: number | string;

  /**
   * 资质名称
   */
  qualificationName: string;

  /**
   * 证书编号
   */
  certificateNumber: string;

  /**
   * 颁发日期
   */
  issueDate: string;

  /**
   * 到期日期
   */
  expiryDate: string;

  /**
   * 证书附件
   */
  certificateAttachment?: string;
}

export interface SupplierForm extends BaseEntity {
  /**
   * 主键ID
   */
  supplierId?: number | string;

  /**
   * 供应商编码（规则：SUP+年+6位序列）
   */
  supplierCode?: string;

  /**
   * 公司名称
   */
  name?: string;

  /**
   * 公司类型
   */
  companyType?: string;

  /**
   * 统一社会信用代码
   */
  creditCode?: string;

  /**
   * 银行账户
   */
  bankAccount?: string;

  /**
   * 开户行全称
   */
  bankName?: string;

  /**
   * 固定电话
   */
  companyPhone?: string;

  /**
   * 地址
   */
  companyAddress?: string;

  /**
   * 官方邮箱
   */
  officialEmail?: string;

  /**
   * 联系人列表
   */
  contacts?: SupplierContactForm[];

  /**
   * 企业资质列表
   */
  qualifications?: SupplierQualificationForm[];
}

export interface SupplierContactForm {
  /**
   * 联系人ID
   */
  contactId?: number | string;

  /**
   * 供应商ID
   */
  supplierId?: number | string;

  /**
   * 姓名
   */
  name?: string;

  /**
   * 职位
   */
  position?: string;

  /**
   * 手机号
   */
  phone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 微信
   */
  wechat?: string;

  /**
   * QQ
   */
  qq?: string;
}

export interface SupplierQualificationForm {
  /**
   * 资质ID
   */
  qualificationId?: number | string;

  /**
   * 供应商ID
   */
  supplierId?: number | string;

  /**
   * 资质名称
   */
  qualificationName?: string;

  /**
   * 证书编号
   */
  certificateNumber?: string;

  /**
   * 颁发日期
   */
  issueDate?: string;

  /**
   * 到期日期
   */
  expiryDate?: string;

  /**
   * 证书附件
   */
  certificateAttachment?: string;
}

export interface SupplierQuery extends PageQuery {
  /**
   * 供应商编码（规则：SUP+年+6位序列）
   */
  supplierCode?: string;

  /**
   * 公司名称
   */
  name?: string;

  /**
   * 公司类型
   */
  companyType?: string;

  /**
   * 统一社会信用代码
   */
  creditCode?: string;

  /**
   * 银行账户
   */
  bankAccount?: string;

  /**
   * 开户行全称
   */
  bankName?: string;

  /**
   * 固定电话
   */
  companyPhone?: string;

  /**
   * 地址
   */
  companyAddress?: string;

  /**
   * 官方邮箱
   */
  officialEmail?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
