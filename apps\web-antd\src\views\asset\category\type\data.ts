import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { z } from '#/adapter/form';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'categoryName',
    label: '分类名称',
  },
  {
    component: 'Input',
    fieldName: 'categoryType',
    label: '分类类型',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '分类名称',
    field: 'categoryName',
  },
  {
    title: '分类类型',
    field: 'categoryType',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'categoryId',
    label: 'categoryId',
  },
  {
    component: 'Input',
    fieldName: 'categoryName',
    label: '分类名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'categoryType',
    help: '使用英文/下划线命名, 如:sys_normal_disable',
    label: '分类类型',
    rules: z
      .string()
      .regex(/^[a-z_]+$/i, { message: '分类类型只能使用英文/下划线命名' }),
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    formItemClass: 'items-baseline',
    label: '备注',
  },
];
