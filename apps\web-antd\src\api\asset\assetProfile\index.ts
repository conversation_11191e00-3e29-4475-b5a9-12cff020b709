import type {
  Dept<PERSON>ree,
  AssetProfileForm,
  AssetProfileQuery,
  AssetProfileVO,
  AssetProfileHisVO,
  AssetOtherVO,
  AssetSummaryVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询资产列表
 * @param params
 * @returns 资产列表
 */
export function assetProfileList(params?: AssetProfileQuery) {
  return requestClient.get<PageResult<AssetProfileVO>>(
    '/asset/assetProfile/list',
    { params },
  );
}

/**
 * 导出资产列表
 * @param params
 * @returns 资产列表
 */
export function assetProfileExport(params?: AssetProfileQuery) {
  return commonExport('/asset/assetProfile/export', params ?? {});
}

/**
 * 查询资产详情
 * @param assetId id
 * @returns 资产详情
 */
export function assetProfileInfo(assetId: ID) {
  return requestClient.get<AssetProfileVO>(`/asset/assetProfile/${assetId}`);
}

/**
 * 新增资产
 * @param data
 * @returns void
 */
export function assetProfileAdd(data: AssetProfileForm) {
  return requestClient.postWithMsg<void>('/asset/assetProfile', data);
}

/**
 * 更新资产
 * @param data
 * @returns void
 */
export function assetProfileUpdate(data: AssetProfileForm) {
  return requestClient.putWithMsg<void>('/asset/assetProfile', data);
}

/**
 * 删除资产
 * @param assetId id
 * @returns void
 */
export function assetProfileRemove(assetId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/asset/assetProfile/${assetId}`);
}

/**
 * 查询品牌字典
 * @param
 * @returns 品牌字典
 */
export function getBrandDic() {
  return requestClient.get<void>(`/asset/brand/getBrandDic`);
}

/**
 * 查询供应商字典
 * @param
 * @returns 供应商字典
 */
export function getSupplierDic() {
  return requestClient.get<void>(`/asset/supplier/getSupplierDic`);
}

/**
 * 导入历史台账
 * @param file Excel文件
 * @returns 导入结果
 */
export function assetProfileImportHistory(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.postWithMsg<{ code: number; msg: string }>(
    '/asset/assetProfile/importHistory',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/**
 * 财务入账
 * @param file Excel文件
 * @returns 导入结果
 */
export function assetProfileImportFinance(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.postWithMsg<void>(
    '/asset/assetProfile/importFinance',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/**
 * 获取部门树
 * @returns 部门树数组
 */
export function getDeptTree() {
  return requestClient.get<DeptTree[]>(`/asset/assetProfile/deptTree`);
}

/**
 * 查询资产历史信息详情
 * @param assetId id
 * @returns 资产详情
 */
export function assetProfileHisInfo(assetId: ID) {
  return requestClient.get<AssetProfileHisVO>(`/asset/assetProfile/getHisInfo/${assetId}`);
}

/**
 * 查询资产其他信息详情
 * @param assetId id
 * @returns 资产详情
 */
export function assetOtherInfo(assetId: ID) {
  return requestClient.get<AssetOtherVO>(`/asset/assetOther/${assetId}`);
}


/**
 * 查询资产汇总信息详情
 * @param assetId id
 * @returns 资产详情
 */
export function assetSummaryInfo(assetId: ID) {
  return requestClient.get<AssetSummaryVO>(`/asset/assetProfile/getSummaryInfo/${assetId}`);
}
