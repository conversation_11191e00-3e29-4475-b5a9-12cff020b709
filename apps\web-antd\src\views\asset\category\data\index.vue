<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { CategoryQuery } from '#/api/asset/category/data/model';

import { nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { getVxePopupContainer } from '@vben/utils';

import { useEventListener } from '@vueuse/core';
import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { categoryList, categoryRemove } from '#/api/asset/category/data';
import { categoryTypeInfo } from '#/api/asset/category/type';

import categoryModal from './category-drawer.vue';
import { columns, querySchema } from './data';

const route = useRoute();
const { closeCurrentTab } = useTabs();
const router = useRouter();

const categoryId = route.query.categoryId as string;
// const disabled = route.query.disabled === 'true';
const categoryType = ref('');
const categoryName = ref('分类数据列表');

async function loadCategoryType() {
  const record = await categoryTypeInfo(categoryId);
  categoryType.value = record.categoryType;
  categoryName.value = record.categoryName;
}

onMounted(loadCategoryType);

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async (_, formValues = {}) => {
        const params: CategoryQuery = {
          ...formValues,
        };
        await loadCategoryType();
        if (categoryType.value) {
          params.categoryType = categoryType.value;
        }
        const resp = await categoryList(params);
        return { rows: resp };
      },
      // 默认请求接口后展开全部 不需要可以删除这段
      querySuccess: () => {
        nextTick(() => {
          expandAll();
        });
      },
    },
  },
  /**
   * 虚拟滚动  默认关闭
   */
  scrollY: {
    enabled: true,
    gt: 0,
  },
  rowConfig: {
    keyField: 'dataId',
  },
  treeConfig: {
    parentField: 'parentId',
    rowField: 'dataId',
    transform: true,
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'asset-category-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({ formOptions, gridOptions });
const [CategoryModal, drawerApi] = useVbenDrawer({
  connectedComponent: categoryModal,
});

function handleAdd(row?: Recordable<any>) {
  drawerApi.setData({
    parentId: row?.dataId,
    categoryType: categoryType.value,
  });
  drawerApi.open();
}

async function handleEdit(row: Recordable<any>) {
  drawerApi.setData({ id: row.dataId, categoryType: categoryType.value });
  drawerApi.open();
}

async function handleDelete(row: Recordable<any>) {
  await categoryRemove(row.dataId);
  await tableApi.query();
}

function expandAll() {
  tableApi.grid?.setAllTreeExpand(true);
}

function collapseAll() {
  tableApi.grid?.setAllTreeExpand(false);
}

function messageHandler(event: MessageEvent) {
  switch (event.data.method) {
    case 'close': {
      // 关闭当前tab
      closeCurrentTab();
      // 跳转到流程定义列表
      router.push('/asset/category/type');
      break;
    }
  }
}
useEventListener('message', messageHandler);
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable :table-title="categoryName">
      <template #toolbar-tools>
        <Space>
          <a-button @click="collapseAll">
            {{ $t('pages.common.collapse') }}
          </a-button>
          <a-button @click="expandAll">
            {{ $t('pages.common.expand') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['asset:category:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['asset:category:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <ghost-button
            class="btn-success"
            v-access:code="['asset:category:edit']"
            @click.stop="handleAdd(row)"
          >
            {{ $t('pages.common.add') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['asset:category:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <CategoryModal @reload="tableApi.query()" />
  </Page>
</template>
