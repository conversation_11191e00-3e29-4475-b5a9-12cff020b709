import type { BaseEntity, PageQuery } from '#/api/common';

export interface BrandVO {
  /**
   * 品牌ID
   */
  brandId: number | string;

  /**
   * 品牌编码 (唯一标识)
   */
  brandCode: string;

  /**
   * 中文全称
   */
  nameZh: string;

  /**
   * 英文全称
   */
  nameEn: string;

  /**
   * 品牌别名 (JSON数组, 例: ["通用电气医疗","GE医疗系统"])
   */
  aliases: string;

  /**
   * 状态: 01-待审 / 02-启用 / 03-停用
   */
  status: string;

  /**
   * 设备分类ID (关联 device_category 表)
   */
  categoryId: number | string;
}

export interface BrandForm extends BaseEntity {
  /**
   * 品牌ID
   */
  brandId?: number | string;

  /**
   * 中文全称
   */
  nameZh?: string;

  /**
   * 英文全称
   */
  nameEn?: string;

  /**
   * 品牌别名 (JSON数组, 例: ["通用电气医疗","GE医疗系统"])
   */
  aliases?: string;

  /**
   * 状态: 01-待审 / 02-启用 / 03-停用
   */
  status?: string;

  /**
   * 设备分类ID (关联 device_category 表)
   */
  categoryId?: number | string;
}

export interface BrandQuery extends PageQuery {
  /**
   * 品牌编码 (唯一标识)
   */
  brandCode?: string;

  /**
   * 中文全称
   */
  nameZh?: string;

  /**
   * 英文全称
   */
  nameEn?: string;

  /**
   * 品牌别名 (JSON数组, 例: ["通用电气医疗","GE医疗系统"])
   */
  aliases?: string;

  /**
   * 状态: 01-待审 / 02-启用 / 03-停用
   */
  status?: string;

  /**
   * 设备分类ID (关联 device_category 表)
   */
  categoryId?: number | string;

  /**
   * 日期范围参数
   */
  params?: any;
}
