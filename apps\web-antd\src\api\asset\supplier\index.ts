import type {
  SupplierContactVO,
  SupplierForm,
  SupplierQualificationVO,
  SupplierQuery,
  SupplierVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询供应商管理列表
 * @param params
 * @returns 供应商管理列表
 */
export function supplierList(params?: SupplierQuery) {
  return requestClient.get<PageResult<SupplierVO>>('/asset/supplier/list', {
    params,
  });
}

/**
 * 导出供应商管理列表
 * @param params
 * @returns 供应商管理列表
 */
export function supplierExport(params?: SupplierQuery) {
  return commonExport('/asset/supplier/export', params ?? {});
}

/**
 * 查询供应商管理详情
 * @param supplierId id
 * @returns 供应商管理详情
 */
export function supplierInfo(supplierId: ID) {
  return requestClient.get<SupplierVO>(`/asset/supplier/${supplierId}`);
}

/**
 * 新增供应商管理
 * @param data
 * @returns void
 */
export function supplierAdd(data: SupplierForm) {
  return requestClient.postWithMsg<void>('/asset/supplier', data);
}

/**
 * 更新供应商管理
 * @param data
 * @returns void
 */
export function supplierUpdate(data: SupplierForm) {
  return requestClient.putWithMsg<void>('/asset/supplier', data);
}

/**
 * 删除供应商管理
 * @param supplierId id
 * @returns void
 */
export function supplierRemove(supplierId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/asset/supplier/${supplierId}`);
}

/**
 * 获取供应商联系人列表
 * @param supplierId 供应商ID
 * @returns 联系人列表
 */
export function getContactList(supplierId: ID) {
  return requestClient.get<SupplierContactVO[]>(
    '/asset/supplier/getContactList',
    {
      params: { supplierId },
    },
  );
}

/**
 * 获取供应商企业资质列表
 * @param supplierId 供应商ID
 * @returns 企业资质列表
 */
export function getQualificationList(supplierId: ID) {
  return requestClient.get<SupplierQualificationVO[]>(
    '/asset/supplier/getQualificationList',
    {
      params: { supplierId },
    },
  );
}
