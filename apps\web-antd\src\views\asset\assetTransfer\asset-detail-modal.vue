<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Card, Col, Descriptions, DescriptionsItem, Row, Spin } from 'ant-design-vue';

import { assetProfileInfo,assetSummaryInfo } from '#/api/asset/assetProfile';
import type { AssetProfileVO, AssetSummaryVO } from '#/api/asset/assetProfile/model';

const loading = ref(false);
const assetDetail = ref<AssetSummaryVO | null>(null);

const [BasicModal, modalApi] = useVbenModal({
  title: '资产详情',
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return;
    }

    loading.value = true;
    try {
      const { assetId } = modalApi.getData() as { assetId: string };
      if (assetId) {
        assetDetail.value = await assetSummaryInfo(assetId);
      }
    } catch (error) {
      console.error('获取资产详情失败:', error);
    } finally {
      loading.value = false;
    }
  },
  onClosed: () => {
    assetDetail.value = null;
  },
});

defineExpose({
  modalApi,
});
</script>

<template>
  <BasicModal>
    <Spin :spinning="loading">
      <div v-if="assetDetail" class="p-4">
        <Row :gutter="[16, 16]">
          <!-- 基本信息 -->
          <Col :span="24">
          <Card title="基本信息" size="small">
            <Descriptions :column="2" bordered size="small">
              <DescriptionsItem label="资产编码">
                {{ assetDetail.assetCode }}
              </DescriptionsItem>
              <DescriptionsItem label="资产名称">
                {{ assetDetail.assetName }}
              </DescriptionsItem>
              <DescriptionsItem label="通用名">
                {{ assetDetail.commonName }}
              </DescriptionsItem>
              <DescriptionsItem label="品牌">
                {{ assetDetail.brand }}
              </DescriptionsItem>
              <DescriptionsItem label="型号">
                {{ assetDetail.model }}
              </DescriptionsItem>
              <DescriptionsItem label="资产类型">
                {{ assetDetail.assetType }}
              </DescriptionsItem>
              <DescriptionsItem label="生产厂家">
                {{ assetDetail.manufacturer }}
              </DescriptionsItem>
              <DescriptionsItem label="供应商">
                {{ assetDetail.supplier }}
              </DescriptionsItem>
              <DescriptionsItem label="产地">
                {{ assetDetail.originCountry || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="计量单位">
                {{ assetDetail.measurementUnit || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="使用年限">
                {{ assetDetail.validityPeriod ? `${assetDetail.validityPeriod}年` : '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="医疗器械注册号">
                {{ assetDetail.registrationNumber || '-' }}
              </DescriptionsItem>
            </Descriptions>
          </Card>
          </Col>

          <!-- 分类信息 -->
          <Col :span="24">
          <Card title="分类信息" size="small">
            <Descriptions :column="2" bordered size="small">
              <DescriptionsItem label="固定资产分类">
                {{ assetDetail.fixedAssetCode || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="专业分类">
                {{ assetDetail.specialtyCategory || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="医疗器械分类">
                {{ assetDetail.medicalDeviceCategory || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="资产类别">
                {{ assetDetail.assetCategory || '-' }}
              </DescriptionsItem>
            </Descriptions>
          </Card>
          </Col>

          <!-- 财务信息 -->
          <Col :span="24">
          <Card title="财务信息" size="small">
            <Descriptions :column="2" bordered size="small">
              <DescriptionsItem label="原值">
                {{ assetDetail.contractPrice ? `¥${Number(assetDetail.contractPrice).toLocaleString()}` : '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="折旧年限">
                {{ assetDetail.depreciationYears ? `${assetDetail.depreciationYears}年` : '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="生产日期">
                {{ assetDetail.productionDate || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="启用日期">
                {{ assetDetail.activationDate || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="折旧方法">
                {{ assetDetail.depreciationMethod || '-' }}
              </DescriptionsItem>
            </Descriptions>
          </Card>
          </Col>

          <!-- 管理信息 -->
          <Col :span="24">
          <Card title="管理信息" size="small">
            <Descriptions :column="2" bordered size="small">
              <DescriptionsItem label="标签二维码">
                {{ assetDetail.qrCode || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="序列号">
                {{ assetDetail.serialNumber || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="使用科室">
                {{ assetDetail.deptSection || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="安装地点">
                {{ assetDetail.installationLocation || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="资产管理员">
                {{ assetDetail.assetManager || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="使用状态">
                {{ assetDetail.useStatus || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="资产状态">
                {{ assetDetail.assetStatus || '-' }}
              </DescriptionsItem>
            </Descriptions>
          </Card>
          </Col>

          <!-- 质量标志 -->
          <!-- <Col :span="24">
          <Card title="质量标志" size="small">
            <Descriptions :column="2" bordered size="small">
              <DescriptionsItem label="物联标签类型">
                {{ assetDetail.tagType || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="计量标志">
                {{ assetDetail.measurementMark || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="质控标志">
                {{ assetDetail.qualityControlMark || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="PM标志">
                {{ assetDetail.pmMark || '-' }}
              </DescriptionsItem>
            </Descriptions>
          </Card>
          </Col> -->

          <!-- 其他信息 -->
          <!-- <Col :span="24">
          <Card title="其他信息" size="small">
            <Descriptions :column="2" bordered size="small">
              <DescriptionsItem label="保修开始日期">
                {{ assetDetail.warrantyStartDate || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="保修截止日期">
                {{ assetDetail.warrantyEndDate || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="创建时间">
                {{ assetDetail.createTime || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="更新时间">
                {{ assetDetail.updateTime || '-' }}
              </DescriptionsItem>
              <DescriptionsItem label="备注" :span="2">
                {{ assetDetail.remark || '-' }}
              </DescriptionsItem>
            </Descriptions>
          </Card>
          </Col> -->
        </Row>
      </div>
    </Spin>
  </BasicModal>
</template>

<style scoped>
:deep(.ant-descriptions-item-label) {
  width: 120px;
  font-weight: 500;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}
</style>
