<script setup lang="ts">
import type { UploadFile } from 'ant-design-vue/es/upload/interface';

import { h, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { InBoxIcon } from '@vben/icons';

import { Modal, Upload } from 'ant-design-vue';

import { assetProfileImportHistory } from '#/api/asset/assetProfile';

const emit = defineEmits<{ reload: [] }>();

const UploadDragger = Upload.Dragger;

const [BasicModal, modalApi] = useVbenModal({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
});

const fileList = ref<UploadFile[]>([]);

async function handleSubmit() {
  try {
    modalApi.modalLoading(true);
    if (fileList.value.length !== 1) {
      handleCancel();
      return;
    }

    const file = fileList.value[0]!.originFileObj as File;
    await assetProfileImportHistory(file);

    emit('reload');
    handleCancel();

    Modal.success({
      content: '历史台账导入成功！',
      title: '提示',
    });
  } catch (error: any) {
    console.warn(error);
    handleCancel();

    Modal.error({
      content: h('div', {
        class: 'max-h-[260px] overflow-y-auto',
        innerHTML:
          error?.response?.data?.msg ||
          error?.message ||
          '导入失败，请检查文件格式和内容',
      }),
      title: '导入失败',
    });
  } finally {
    modalApi.modalLoading(false);
  }
}

function handleCancel() {
  modalApi.close();
  fileList.value = [];
}
</script>

<template>
  <BasicModal
    :close-on-click-modal="false"
    :fullscreen-button="false"
    title="导入历史台账"
  >
    <!-- 文件上传区域 -->
    <UploadDragger
      v-model:file-list="fileList"
      :before-upload="() => false"
      :max-count="1"
      :show-upload-list="true"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    >
      <p class="ant-upload-drag-icon flex items-center justify-center">
        <InBoxIcon class="text-primary size-[48px]" />
      </p>
      <p class="ant-upload-text">点击或者拖拽到此处上传文件</p>
      <p class="ant-upload-hint">支持单个文件上传，仅支持 Excel 格式文件</p>
    </UploadDragger>

    <!-- 提示信息 -->
    <div class="mt-4 space-y-3">
      <div class="flex items-center gap-2">
        <span class="text-gray-600">支持格式：Excel (.xlsx, .xls), CSV</span>
      </div>

      <div class="rounded-md border border-blue-200 bg-blue-50 p-3">
        <div class="mb-2 font-medium text-blue-800">导入说明：</div>
        <ul class="space-y-1 text-sm text-blue-700">
          <li>• 请确保Excel文件格式正确</li>
          <li>
            •
            文件应包含必要的资产信息字段：资产编码、资产名称、规格型号、序列号、使用科室等
          </li>
        </ul>
      </div>
    </div>
  </BasicModal>
</template>
