import type { VbenFormSchema } from '@vben/common-ui';

import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';

/**
 * 基本信息表单配置
 */
export function basicInfoSchema(): VbenFormSchema[] {
  return [
    {
      label: '供应商ID',
      fieldName: 'supplierId',
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入供应商编码',
        disabled: true,
      },
      fieldName: 'supplierCode',
      label: '供应商编码',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入公司名称',
      },
      fieldName: 'name',
      label: '公司名称',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入统一社会信用代码',
      },
      fieldName: 'creditCode',
      label: '统一社会信用代码',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择公司类型',
        mode: 'multiple',
        options: getDictOptions(DictEnum.COMPANY_TYPE),
      },
      fieldName: 'companyType',
      label: '公司类型',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入银行账户',
      },
      fieldName: 'bankAccount',
      label: '银行账户',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入开户行全称',
      },
      fieldName: 'bankName',
      label: '开户行全称',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入固定电话',
      },
      fieldName: 'companyPhone',
      label: '固定电话',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入地址',
        rows: 3,
      },
      fieldName: 'companyAddress',
      label: '地址',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入官方邮箱',
        type: 'email',
      },
      fieldName: 'officialEmail',
      label: '官方邮箱',
    },
  ];
}

/**
 * 创建单个联系人表单配置
 */
export function createContactSchema(index: number): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
      },
      fieldName: `contacts[${index}].name`,
      label: `姓名 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入职位',
      },
      fieldName: `contacts[${index}].position`,
      label: `职位 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入手机号',
      },
      fieldName: `contacts[${index}].phone`,
      label: `手机号 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入邮箱',
        type: 'email',
      },
      fieldName: `contacts[${index}].email`,
      label: `邮箱 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入微信',
      },
      fieldName: `contacts[${index}].wechat`,
      label: `微信 ${index + 1}`,
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入QQ',
      },
      fieldName: `contacts[${index}].qq`,
      label: `QQ ${index + 1}`,
    },
  ];
}

/**
 * 创建单个企业资质表单配置
 */
export function createQualificationSchema(index: number): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入资质名称',
      },
      fieldName: `qualifications[${index}].qualificationName`,
      label: `资质名称 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入证书编号',
      },
      fieldName: `qualifications[${index}].certificateNumber`,
      label: `证书编号 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择颁发日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: `qualifications[${index}].issueDate`,
      label: `颁发日期 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择到期日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: `qualifications[${index}].expiryDate`,
      label: `到期日期 ${index + 1}`,
      rules: 'required',
    },
    {
      component: 'FileUpload',
      componentProps: {
        placeholder: '请上传证书附件',
        accept: '.pdf,.jpg,.jpeg,.png,.doc,.docx',
        maxCount: 1,
        listType: 'text',
      },
      fieldName: `qualifications[${index}].certificateAttachment`,
      label: `证书附件 ${index + 1}`,
    },
    {
      component: 'Br',
      fieldName: '',
      label: '',
    },
  ];
}

/**
 * 联系人基础表单配置
 */
export function contactSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
      },
      fieldName: 'contacts[0].name',
      label: '姓名',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入职位',
      },
      fieldName: 'contacts[0].position',
      label: '职位',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入手机号',
      },
      fieldName: 'contacts[0].phone',
      label: '手机号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入邮箱',
        type: 'email',
      },
      fieldName: 'contacts[0].email',
      label: '邮箱',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入微信',
      },
      fieldName: 'contacts[0].wechat',
      label: '微信',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入QQ',
      },
      fieldName: 'contacts[0].qq',
      label: 'QQ',
    },
  ];
}
