<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { CategoryType } from '#/api/asset/category/type/model';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  categoryTypeExport,
  categoryTypeList,
  categoryTypeRemove,
  refreshCategoryTypeCache,
} from '#/api/asset/category/type';
import { commonDownloadExcel } from '#/utils/file/download';

import categoryTypeDrawer from './category-type-drawer.vue';
import { columns, querySchema } from './data';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await categoryTypeList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'categoryId',
  },
  id: 'asset-category-type-index',
};

const lastCategoryType = ref('');

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    cellClick: (e) => {
      const { row } = e;
      lastCategoryType.value = row.categoryType;
      handleCategoryData(row, false);
    },
  },
});
const [CategoryTypeModal, drawerApi] = useVbenDrawer({
  connectedComponent: categoryTypeDrawer,
});

function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

async function handleEdit(record: CategoryType) {
  drawerApi.setData({ id: record.categoryId });
  drawerApi.open();
}

async function handleDelete(row: CategoryType) {
  await categoryTypeRemove([row.categoryId]);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: CategoryType) => row.categoryId);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await categoryTypeRemove(ids);
      await tableApi.query();
    },
  });
}

async function handleRefreshCache() {
  await refreshCategoryTypeCache();
  await tableApi.query();
}

function handleDownloadExcel() {
  commonDownloadExcel(
    categoryTypeExport,
    '分类类型数据',
    tableApi.formApi.form.values,
  );
}

const router = useRouter();
/**
 * 配置分类数据
 * @param row
 * @param disabled  true为预览，false为配置
 */
function handleCategoryData(row: any, disabled: boolean) {
  router.push({
    path: '/Informatization/category/data',
    query: { categoryId: row.categoryId, disabled: String(disabled) },
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable id="category-type" table-title="分类类型列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['asset:category:edit']"
            @click="handleRefreshCache"
          >
            刷新缓存
          </a-button>
          <a-button
            v-access:code="['asset:category:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['asset:category:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['asset:category:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['asset:category:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="
              (node) => getVxePopupContainer(node, 'category-type')
            "
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['asset:category:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <CategoryTypeModal @reload="tableApi.query()" />
  </Page>
</template>
