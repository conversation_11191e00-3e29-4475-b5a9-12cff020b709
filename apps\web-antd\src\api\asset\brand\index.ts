import type { BrandForm, BrandQuery, BrandVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询品牌信息列表
 * @param params
 * @returns 品牌信息列表
 */
export function brandList(params?: BrandQuery) {
  return requestClient.get<PageResult<BrandVO>>('/asset/brand/list', {
    params,
  });
}

/**
 * 导出品牌信息列表
 * @param params
 * @returns 品牌信息列表
 */
export function brandExport(params?: BrandQuery) {
  return commonExport('/asset/brand/export', params ?? {});
}

/**
 * 查询品牌信息详情
 * @param brandId id
 * @returns 品牌信息详情
 */
export function brandInfo(brandId: ID) {
  return requestClient.get<BrandVO>(`/asset/brand/${brandId}`);
}

/**
 * 新增品牌信息
 * @param data
 * @returns void
 */
export function brandAdd(data: BrandForm) {
  return requestClient.postWithMsg<void>('/asset/brand', data);
}

/**
 * 更新品牌信息
 * @param data
 * @returns void
 */
export function brandUpdate(data: BrandForm) {
  return requestClient.putWithMsg<void>('/asset/brand', data);
}

/**
 * 删除品牌信息
 * @param brandId id
 * @returns void
 */
export function brandRemove(brandId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/asset/brand/${brandId}`);
}
