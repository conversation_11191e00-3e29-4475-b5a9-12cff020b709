<script setup lang="ts">
import type { TreeSelectProps } from 'ant-design-vue/es/vc-tree-select/TreeSelect';

import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  InventoryDetailForm,
  InventoryDetailVO,
} from '#/api/asset/inventoryDetail/model';
import type { InventoryTaskForm } from '#/api/asset/inventoryTask/model';

import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { addFullName } from '@vben/utils';

import { Card, Col, message, Progress, Row, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  inventoryDetailList,
  inventoryDetailUpdateStatus,
} from '#/api/asset/inventoryDetail';
import {
  inventoryTaskChangeStatus,
  inventoryTaskInfo,
} from '#/api/asset/inventoryTask';
import { getDeptTree } from '#/api/system/user';
import { renderDictLable } from '#/utils/render';

import { columns, querySchema } from './data';
import inventoryDetailModal from './inventoryDetail-modal.vue';

const route = useRoute();
const taskId = route.query.taskId as string;
// 加载盘点任务信息
async function loadInventoryTaskInfo() {
  const record = await inventoryTaskInfo(taskId);
  // 赋值
  inventoryInfo.value.title = record.taskName;
  inventoryInfo.value.range = renderDictLable(
    record.inventoryScope,
    DictEnum.AST_INVENTORY_SCOPE,
  );
  inventoryInfo.value.planTime = `${record.taskStartTime}~${record.taskEndTime}`;
  inventoryInfo.value.person = record.inventoryPerson;
  inventoryInfo.value.status = record.inventoryStatus;
}
/**
 * 加载科室树形数据并更新表单中部门选择组件的配置
 * 该函数会：
 * 1. 获取科室树数据
 * 2. 处理树数据添加全名字段
 * 3. 更新表单中指定字段的schema配置，以支持树形选择
 */
async function loadDeptTree() {
  const treeData = ref<TreeSelectProps['treeData']>([]);
  const deptTree = await getDeptTree(); // 获取科室数据
  addFullName(deptTree, 'label', ' / ');
  treeData.value = deptTree;
  const fieldNames = ['useDept'];
  const schemaUpdates = fieldNames.map((fieldName) => ({
    componentProps: {
      fieldNames: { label: 'label', value: 'id' },
      showSearch: true,
      treeData,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      // 选中后显示在输入框的值
      treeNodeLabelProp: 'fullName',
      // 筛选的字段
      treeNodeFilterProp: 'deptName',
    },
    fieldName,
  }));
  tableApi.formApi.updateSchema(schemaUpdates);
}

// 修改盘点任务状态为完成
async function completeInventoryTask() {
  if (!taskId) {
    return;
  }
  const data: InventoryTaskForm = {
    taskId,
    inventoryStatus: 2,
  };
  try {
    await inventoryTaskChangeStatus(data);
    inventoryInfo.value.status = 2;
  } catch {}
}

// 修改盘点任务详情状态
async function updateInventoryDetailStatus(detailId: string, status: string) {
  console.log('updateInventoryDetailStatus', detailId, status);
  if (!detailId) {
    return;
  }
  const data: InventoryDetailForm = {
    detailId,
    inventoryStatus: status,
  };
  try {
    await inventoryDetailUpdateStatus(data);
    refreshTable();
  } catch {}
}

onMounted(async () => {
  await loadInventoryTaskInfo();
  await loadDeptTree();
});

// 盘点信息
const inventoryInfo = ref({
  title: '',
  range: '' as string | undefined,
  planTime: '',
  person: '',
  totalAssets: 0,
  completedAssets: 0,
  surplusAssets: 0,
  shortageAssets: 0,
  progress: 0,
  status: 0,
});

const tableKey = ref(0);
function refreshTable() {
  tableKey.value += 1;
}

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const data = await inventoryDetailList({
          taskId,
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });

        inventoryInfo.value.totalAssets = data.rows.length;
        inventoryInfo.value.completedAssets = data.rows.filter(
          (item: InventoryDetailVO) => item.inventoryStatus !== '0',
        ).length;
        inventoryInfo.value.surplusAssets = data.rows.filter(
          (item: InventoryDetailVO) => item.inventoryStatus === '2',
        ).length;
        inventoryInfo.value.shortageAssets = data.rows.filter(
          (item: InventoryDetailVO) => item.inventoryStatus === '3',
        ).length;
        inventoryInfo.value.progress =
          (inventoryInfo.value.completedAssets /
            inventoryInfo.value.totalAssets) *
          100;
        return data;
      },
    },
  },
  rowConfig: {
    keyField: 'detailId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'asset-inventoryDetail-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [InventoryDetailModal] = useVbenModal({
  connectedComponent: inventoryDetailModal,
});

// function handleAdd() {
//   modalApi.setData({});
//   modalApi.open();
// }

function todo() {
  message.info('敬请期待');
}

// async function handleEdit(row: Required<InventoryDetailForm>) {
//   modalApi.setData({ id: row.detailId });
//   modalApi.open();
// }
// function handleMultiDelete() {
//   const rows = tableApi.grid.getCheckboxRecords();
//   const ids = rows.map((row: Required<InventoryDetailForm>) => row.detailId);
//   Modal.confirm({
//     title: '提示',
//     okType: 'danger',
//     content: `确认删除选中的${ids.length}条记录吗？`,
//     onOk: async () => {
//       await inventoryDetailRemove(ids);
//       await tableApi.query();
//     },
//   });
// }

// function handleDownloadExcel() {
//   commonDownloadExcel(
//     inventoryDetailExport,
//     '资产盘点详情数据',
//     tableApi.formApi.form.values,
//     {
//       fieldMappingTime: formOptions.fieldMappingTime,
//     },
//   );
// }
</script>

<template>
  <Page :auto-content-height="true">
    <Card style="margin-bottom: 0.75em">
      <!-- 标题和描述 -->
      <div class="inventory-header">
        <h2>{{ inventoryInfo.title }}</h2>
        <p>
          盘点范围：{{ inventoryInfo.range }} | 计划时间：{{
            inventoryInfo.planTime
          }}
          | 盘点人：{{ inventoryInfo.person }}
        </p>
      </div>
    </Card>
    <Card style="margin-bottom: 0.75em">
      <!-- 盘点进度 -->
      <h3>盘点进度</h3>
      <Row justify="center" align="middle" style="margin-bottom: 2em">
        <Col :span="6">
          <p class="card-number" style="color: blue">
            {{ inventoryInfo.totalAssets }}
          </p>
          <p class="card-label">总资产数</p>
        </Col>
        <Col :span="6">
          <p class="card-number" style="color: green">
            {{ inventoryInfo.completedAssets }}
          </p>
          <p class="card-label">已盘点</p>
        </Col>
        <Col :span="6">
          <p class="card-number" style="color: orange">
            {{ inventoryInfo.surplusAssets }}
          </p>
          <p class="card-label">盘盈</p>
        </Col>
        <Col :span="6">
          <p class="card-number" style="color: red">
            {{ inventoryInfo.shortageAssets }}
          </p>
          <p class="card-label">盘亏</p>
        </Col>
      </Row>
      <div style="margin-bottom: 2em">
        盘点进度：{{ inventoryInfo.completedAssets }}/{{
          inventoryInfo.totalAssets
        }}
        <Progress :percent="inventoryInfo.progress" />
      </div>
      <div>
        <Space style="margin-right: 1em">
          <ghost-button
            @click="completeInventoryTask"
            type="primary"
            v-access:code="['asset:inventoryDetail:add']"
            :disabled="
              Number(inventoryInfo.status) !== 1 ||
              inventoryInfo.totalAssets !== inventoryInfo.completedAssets
            "
          >
            {{ $t('pages.common.inventoryComplete') }}
          </ghost-button>
        </Space>
        <Space>
          <ghost-button
            type="primary"
            v-access:code="['asset:inventoryDetail:add']"
            @click="todo"
            :disabled="Number(inventoryInfo.status) !== 2"
          >
            {{ $t('pages.common.downloadList') }}
          </ghost-button>
        </Space>
      </div>
    </Card>
    <BasicTable :key="tableKey" table-title="资产盘点详情列表">
      <!-- <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['asset:inventoryDetail:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['asset:inventoryDetail:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['asset:inventoryDetail:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template> -->
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['asset:inventoryDetail:edit']"
            :disabled="Number(row.inventoryStatus) === 1"
            @click="updateInventoryDetailStatus(row.detailId, '1')"
          >
            设为正常
          </ghost-button>
          <ghost-button
            v-access:code="['asset:inventoryDetail:edit']"
            :disabled="Number(row.inventoryStatus) === 2"
            @click="updateInventoryDetailStatus(row.detailId, '2')"
          >
            设为盘盈
          </ghost-button>

          <ghost-button
            danger
            v-access:code="['asset:inventoryDetail:edit']"
            :disabled="Number(row.inventoryStatus) === 3"
            @click="updateInventoryDetailStatus(row.detailId, '3')"
          >
            设为盘亏
          </ghost-button>
        </Space>
      </template>
    </BasicTable>
    <InventoryDetailModal @reload="tableApi.query()" />
  </Page>
</template>
<style scoped>
h2 {
  font-size: 1.5em;
  font-weight: bold;
}

h3 {
  font-size: 1.3em;
  font-weight: bold;
}

.card-number {
  margin-bottom: 0.5em;
  font-size: 1.5em; /* 字体变大 */
  font-weight: bold;
  text-align: center;
}

.card-label {
  font-size: 1em;
  font-weight: bold;
  text-align: center;
}
</style>
