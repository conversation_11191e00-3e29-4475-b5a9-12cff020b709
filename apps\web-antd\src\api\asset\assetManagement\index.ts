import type {
  AssetManagementForm,
  AssetManagementQuery,
  AssetManagementVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询资产管理信息列表
 * @param params
 * @returns 资产管理信息列表
 */
export function assetManagementList(params?: AssetManagementQuery) {
  return requestClient.get<PageResult<AssetManagementVO>>(
    '/asset/assetManagement/list',
    { params },
  );
}

/**
 * 导出资产管理信息列表
 * @param params
 * @returns 资产管理信息列表
 */
export function assetManagementExport(params?: AssetManagementQuery) {
  return commonExport('/asset/assetManagement/export', params ?? {});
}

/**
 * 查询资产管理信息详情
 * @param managementId id
 * @returns 资产管理信息详情
 */
export function assetManagementInfo(managementId: ID) {
  return requestClient.get<AssetManagementVO>(
    `/asset/assetManagement/${managementId}`,
  );
}

/**
 * 新增资产管理信息
 * @param data
 * @returns void
 */
export function assetManagementAdd(data: AssetManagementForm) {
  return requestClient.postWithMsg<void>('/asset/assetManagement', data);
}

/**
 * 更新资产管理信息
 * @param data
 * @returns void
 */
export function assetManagementUpdate(data: AssetManagementForm) {
  return requestClient.putWithMsg<void>('/asset/assetManagement', data);
}

/**
 * 删除资产管理信息
 * @param managementId id
 * @returns void
 */
export function assetManagementRemove(managementId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(
    `/asset/assetManagement/${managementId}`,
  );
}
