import type { AssetRetireVO, AssetRetireForm, AssetRetireQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询资产退库列表
* @param params
* @returns 资产退库列表
*/
export function assetRetireList(params?: AssetRetireQuery) {
  return requestClient.get<PageResult<AssetRetireVO>>('/asset/assetRetire/list', { params });
}

/**
 * 导出资产退库列表
 * @param params
 * @returns 资产退库列表
 */
export function assetRetireExport(params?: AssetRetireQuery) {
  return commonExport('/asset/assetRetire/export', params ?? {});
}

/**
 * 查询资产退库详情
 * @param retireId id
 * @returns 资产退库详情
 */
export function assetRetireInfo(retireId: ID) {
  return requestClient.get<AssetRetireVO>(`/asset/assetRetire/${retireId}`);
}

/**
 * 新增资产退库
 * @param data
 * @returns void
 */
export function assetRetireAdd(data: AssetRetireForm) {
  return requestClient.postWithMsg<void>('/asset/assetRetire', data);
}

/**
 * 更新资产退库
 * @param data
 * @returns void
 */
export function assetRetireUpdate(data: AssetRetireForm) {
  return requestClient.putWithMsg<void>('/asset/assetRetire', data);
}

/**
 * 删除资产退库
 * @param retireId id
 * @returns void
 */
export function assetRetireRemove(retireId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/asset/assetRetire/${retireId}`);
}
