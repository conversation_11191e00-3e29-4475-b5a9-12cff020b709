<script setup lang="ts">
import type { AssetTransferVO } from '#/api/asset/assetTransfer/model';

import { onMounted, ref } from 'vue';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';
import dayjs from 'dayjs';

import { renderDictLable } from '#/utils/render';
import { DictEnum } from '@vben/constants';
import { getDeptTree } from '#/api/asset/assetProfile';
import { findUserInfo } from '#/api/system/user';

defineOptions({
  name: 'TransferDescription',
  inheritAttrs: false,
});

const props = defineProps<{ data: AssetTransferVO }>();

// 部门和用户信息缓存
const deptMap = ref<Map<string, string>>(new Map());
const userMap = ref<Map<number, string>>(new Map());

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 格式化日期时间
function formatDateTime(date: string) {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
}

// 获取部门名称
function getDeptName(deptId: string | number): string {
  const key = String(deptId);
  return deptMap.value.get(key) || `部门${deptId}`;
}

// 获取用户名称
function getUserName(userId: number): string {
  return userMap.value.get(userId) || `用户${userId}`;
}

// 加载部门信息
async function loadDeptInfo() {
  try {
    const deptTree = await getDeptTree();
    const flattenDepts = (depts: any[]): void => {
      depts.forEach(dept => {
        deptMap.value.set(String(dept.value), dept.label);
        if (dept.children && dept.children.length > 0) {
          flattenDepts(dept.children);
        }
      });
    };
    flattenDepts(deptTree);
  } catch (error) {
    console.error('加载部门信息失败:', error);
  }
}

// 加载用户信息
async function loadUserInfo(userId: number) {
  if (userMap.value.has(userId)) {
    return;
  }

  try {
    const userInfoResponse = await findUserInfo(String(userId));
    const user = userInfoResponse.user;
    if (user) {
      userMap.value.set(userId, user.nickName || user.userName || String(userId));
    }
  } catch (error) {
    console.error('加载用户信息失败:', error);
  }
}

// 组件挂载时加载相关信息
onMounted(async () => {
  await loadDeptInfo();
  if (props.data.applicant) {
    await loadUserInfo(props.data.applicant);
  }
});
</script>

<template>
  <Descriptions :column="2" size="middle" bordered>
    <DescriptionsItem label="转科单号">
      {{ data.transferId }}
    </DescriptionsItem>
    <DescriptionsItem label="申请日期">
      {{ formatDate(data.applicationDate) }}
    </DescriptionsItem>
    
    <DescriptionsItem label="资产编码">
      {{ data.assetCode }}
    </DescriptionsItem>
    <DescriptionsItem label="资产名称">
      {{ data.assetName }}
    </DescriptionsItem>
    
    <DescriptionsItem label="品牌">
      {{ data.brand || '无' }}
    </DescriptionsItem>
    <DescriptionsItem label="型号">
      {{ data.model || '无' }}
    </DescriptionsItem>
    
    <DescriptionsItem label="出厂编码">
      {{ data.serialNumber || '无' }}
    </DescriptionsItem>
    <DescriptionsItem label="管理部门">
      {{ getDeptName(data.manageDept) }}
    </DescriptionsItem>
    
    <DescriptionsItem label="转出科室">
      {{ getDeptName(data.deptOutId) }}
    </DescriptionsItem>
    <DescriptionsItem label="转入科室">
      {{ getDeptName(data.deptInId) }}
    </DescriptionsItem>
    
    <DescriptionsItem label="申请人">
      {{ getUserName(data.applicant) }}
    </DescriptionsItem>
    <DescriptionsItem label="状态">
      {{ renderDictLable(data.status, DictEnum.WF_BUSINESS_STATUS) }}
    </DescriptionsItem>
  </Descriptions>
</template>
