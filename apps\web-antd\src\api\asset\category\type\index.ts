import type { CategoryType } from './model';

import type { ID, IDS, PageQuery, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  categoryOptionSelectList = '/asset/category/type/optionselect',
  categoryTypeExport = '/asset/category/type/export',
  categoryTypeList = '/asset/category/type/list',
  categoryTypeRefreshCache = '/asset/category/type/refreshCache',
  root = '/asset/category/type',
  categoryTree = '/asset/category/type/categoryTree',
}

/**
 * 获取字典类型列表
 * @param params 请求参数
 * @returns list
 */
export function categoryTypeList(params?: PageQuery) {
  return requestClient.get<PageResult<CategoryType>>(Api.categoryTypeList, {
    params,
  });
}

/**
 * 导出字典类型列表
 * @param data 表单参数
 * @returns blob
 */
export function categoryTypeExport(data: Partial<CategoryType>) {
  return commonExport(Api.categoryTypeExport, data);
}

/**
 * 删除字典类型
 * @param categoryIds 字典类型id数组
 * @returns void
 */
export function categoryTypeRemove(categoryIds: IDS) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${categoryIds}`);
}

/**
 * 刷新字典缓存
 * @returns void
 */
export function refreshCategoryTypeCache() {
  return requestClient.deleteWithMsg<void>(Api.categoryTypeRefreshCache);
}

/**
 * 新增
 * @param data 表单参数
 * @returns void
 */
export function categoryTypeAdd(data: Partial<CategoryType>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改
 * @param data 表单参数
 * @returns void
 */
export function categoryTypeUpdate(data: Partial<CategoryType>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 查询详情
 * @param categoryId 字典类型id
 * @returns 信息
 */
export function categoryTypeInfo(categoryId: ID) {
  return requestClient.get<CategoryType>(`${Api.root}/${categoryId}`);
}

/**
 * 这个在ele用到 v5用不上
 * 下拉框  返回值和list一样
 * @returns options
 */
export function categoryOptionSelectList() {
  return requestClient.get<CategoryType[]>(Api.categoryOptionSelectList);
}

/**
 * 获取通用分类树
 * @param categoryType 分类类型
 * @returns options
 */
export function categoryTree(categoryType?: string) {
  if (categoryType) {
    return requestClient.get<CategoryType[]>(Api.categoryTree, {
      params: { categoryType },
    });
  }
  return requestClient.get<CategoryType[]>(Api.categoryOptionSelectList);
}
