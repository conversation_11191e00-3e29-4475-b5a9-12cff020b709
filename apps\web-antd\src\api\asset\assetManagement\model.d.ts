import type { BaseEntity, PageQuery } from '#/api/common';

export interface AssetManagementVO {
  /**
   * 管理信息ID
   */
  managementId: number | string;

  /**
   * 资产ID
   */
  assetId: number | string;

  /**
   * 管理部门
   */
  manageDept: number;

  /**
   * 使用科室
   */
  deptSection: number;

  /**
   * 安装地点
   */
  installationLocation: string;

  /**
   * 资产管理员
   */
  assetManager: number;

  /**
   * 使用科室
   */
  useDept: number;

  /**
   * 保修开始日期
   */
  warrantyStartDate: string;

  /**
   * 保修截止日期
   */
  warrantyEndDate: string;

  /**
   * 标签二维码
   */
  qrCode: string;

  /**
   * 序列号
   */
  serialNumber: string;

  /**
   * 实物照片(多个ossId,逗号分隔)
   */
  itemPhotos: string;

  /**
   * 生产日期
   */
  productionDate: string;

  /**
   * 资产状态
   */
  assetStatus: string;

  /**
   * 使用状态
   */
  useStatus: string;
}

export interface AssetManagementForm extends BaseEntity {
  /**
   * 管理信息ID
   */
  managementId?: number | string;

  /**
   * 资产ID
   */
  assetId?: number | string;

  /**
   * 管理部门
   */
  manageDept?: number;

  /**
   * 使用科室
   */
  deptSection?: number;

  /**
   * 安装地点
   */
  installationLocation?: string;

  /**
   * 资产管理员
   */
  assetManager?: number;

  /**
   * 使用科室
   */
  useDept?: number;

  /**
   * 保修开始日期
   */
  warrantyStartDate?: string;

  /**
   * 保修截止日期
   */
  warrantyEndDate?: string;

  /**
   * 标签二维码
   */
  qrCode?: string;

  /**
   * 序列号
   */
  serialNumber?: string;

  /**
   * 实物照片(多个ossId,逗号分隔)
   */
  itemPhotos?: string;

  /**
   * 生产日期
   */
  productionDate?: string;

  /**
   * 资产状态
   */
  assetStatus?: string;

  /**
   * 使用状态
   */
  useStatus?: string;
}

export interface AssetManagementQuery extends PageQuery {
  /**
   * 资产ID
   */
  assetId?: number | string;

  /**
   * 管理部门
   */
  manageDept?: number;

  /**
   * 使用科室
   */
  deptSection?: number;

  /**
   * 安装地点
   */
  installationLocation?: string;

  /**
   * 资产管理员
   */
  assetManager?: number;

  /**
   * 使用科室
   */
  useDept?: number;

  /**
   * 保修开始日期
   */
  warrantyStartDate?: string;

  /**
   * 保修截止日期
   */
  warrantyEndDate?: string;

  /**
   * 标签二维码
   */
  qrCode?: string;

  /**
   * 序列号
   */
  serialNumber?: string;

  /**
   * 实物照片(多个ossId,逗号分隔)
   */
  itemPhotos?: string;

  /**
   * 生产日期
   */
  productionDate?: string;

  /**
   * 资产状态
   */
  assetStatus?: string;

  /**
   * 使用状态
   */
  useStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
