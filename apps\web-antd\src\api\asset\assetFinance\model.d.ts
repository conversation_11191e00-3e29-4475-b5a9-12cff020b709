import type { BaseEntity, PageQuery } from '#/api/common';

export interface AssetFinanceVO {
  /**
   * 财务信息ID
   */
  financeId: number | string;

  /**
   * 资产ID
   */
  assetId: number | string;

  /**
   * 原值(元)
   */
  contractPrice: number;

  /**
   * 启用日期
   */
  activationDate: string;

  /**
   * 折旧方法
   */
  depreciationMethod: string;

  /**
   * 折旧年限
   */
  depreciationYears: number;

  /**
   * 残值率(%)
   */
  residualRate: number | string;

  /**
   * 财政拨款
   */
  financeAppropriation: number;

  /**
   * 科研专项
   */
  researchProject: number;

  /**
   * 自筹资金
   */
  selfRaised: number;
}

export interface AssetFinanceForm extends BaseEntity {
  /**
   * 财务信息ID
   */
  financeId?: number | string;

  /**
   * 资产ID
   */
  assetId?: number | string;

  /**
   * 原值(元)
   */
  contractPrice?: number;

  /**
   * 启用日期
   */
  activationDate?: string;

  /**
   * 折旧方法
   */
  depreciationMethod?: string;

  /**
   * 折旧年限
   */
  depreciationYears?: number;

  /**
   * 残值率(%)
   */
  residualRate?: number | string;

  /**
   * 财政拨款
   */
  financeAppropriation?: number;

  /**
   * 科研专项
   */
  researchProject?: number;

  /**
   * 自筹资金
   */
  selfRaised?: number;
}

export interface AssetFinanceQuery extends PageQuery {
  /**
   * 资产ID
   */
  assetId?: number | string;

  /**
   * 原值(元)
   */
  contractPrice?: number;

  /**
   * 启用日期
   */
  activationDate?: string;

  /**
   * 折旧方法
   */
  depreciationMethod?: string;

  /**
   * 折旧年限
   */
  depreciationYears?: number;

  /**
   * 残值率(%)
   */
  residualRate?: number | string;

  /**
   * 财政拨款
   */
  financeAppropriation?: number;

  /**
   * 科研专项
   */
  researchProject?: number;

  /**
   * 自筹资金
   */
  selfRaised?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
