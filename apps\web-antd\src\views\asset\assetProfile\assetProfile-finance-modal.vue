<script setup lang="ts">
import type { UploadFile } from 'ant-design-vue/es/upload/interface';

import { h, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { InBoxIcon } from '@vben/icons';

import { Modal, Upload } from 'ant-design-vue';

import { assetProfileImportFinance } from '#/api/asset/assetProfile';

const emit = defineEmits<{ reload: [] }>();

const UploadDragger = Upload.Dragger;

const [BasicModal, modalApi] = useVbenModal({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
});

const fileList = ref<UploadFile[]>([]);

async function handleSubmit() {
  try {
    modalApi.modalLoading(true);
    if (fileList.value.length !== 1) {
      handleCancel();
      return;
    }

    const file = fileList.value[0]!.originFileObj as File;
    await assetProfileImportFinance(file);

    emit('reload');
    handleCancel();

    Modal.success({
      content: '财务入账导入成功！',
      title: '提示',
    });
  } catch (error: any) {
    console.warn(error);
    handleCancel();

    Modal.error({
      content: h('div', {
        class: 'max-h-[260px] overflow-y-auto',
        innerHTML:
          error?.response?.data?.msg ||
          error?.message ||
          '导入失败，请检查文件格式和内容',
      }),
      title: '导入失败',
    });
  } finally {
    modalApi.modalLoading(false);
  }
}

function handleCancel() {
  modalApi.close();
  fileList.value = [];
}

// 下载导入模板（如果有的话）
// function handleDownloadTemplate() {
//   // 这里可以添加下载模板的逻辑
//   // commonDownloadExcel(downloadImportTemplate, '财务入账导入模板');
//   Modal.info({
//     title: '提示',
//     content: '请联系管理员获取导入模板',
//   });
// }
</script>

<template>
  <BasicModal
    :close-on-click-modal="false"
    :fullscreen-button="false"
    title="财务入账"
  >
    <!-- 文件上传区域 -->
    <UploadDragger
      v-model:file-list="fileList"
      :before-upload="() => false"
      :max-count="1"
      :show-upload-list="true"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    >
      <p class="ant-upload-drag-icon flex items-center justify-center">
        <InBoxIcon class="text-primary size-[48px]" />
      </p>
      <p class="ant-upload-text">点击或者拖拽到此处上传文件</p>
      <p class="ant-upload-hint">支持单个文件上传，仅支持 Excel 格式文件</p>
    </UploadDragger>

    <!-- 提示信息 -->
    <div class="mt-4 space-y-3">
      <div class="flex items-center gap-2">
        <span class="text-gray-600">支持格式：Excel (.xlsx, .xls), CSV</span>
        <!-- <a-button
          type="link"
          @click="handleDownloadTemplate"
        >
          <div class="flex items-center gap-[4px]">
            <ExcelIcon />
            <span>下载模板</span>
          </div>
        </a-button> -->
      </div>

      <div class="rounded-md border border-blue-200 bg-blue-50 p-3">
        <div class="mb-2 font-medium text-blue-800">财务入账说明：</div>
        <ul class="space-y-1 text-sm text-blue-700">
          <li>• 请确保Excel文件格式正确</li>
          <li>
            •
            文件应包含必要字段：标签二维码、资产编号、资产名称、资产原值、资产分类、记账日期、记账凭证号、规格型号、品牌、使用部门、资产管理部门
          </li>
        </ul>
      </div>
    </div>
  </BasicModal>
</template>
