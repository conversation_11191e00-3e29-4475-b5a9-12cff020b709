import type { BaseEntity, PageQuery } from '#/api/common';

export interface InventoryDetailVO {
  /**
   * 主键详情ID
   */
  detailId: number | string;

  /**
   * 物联二维码
   */
  qrCode: string;

  /**
   * 固定资产编码
   */
  assetCode: string;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 使用科室
   */
  useDept: string;

  /**
   * 使用地址
   */
  useLocal: string;

  /**
   * 盘点状态
   */
  inventoryStatus: string;

  /**
   * 备注
   */
  remark: string;
}

export interface InventoryDetailForm extends BaseEntity {
  /**
   * 主键详情ID
   */
  detailId?: number | string;

  /**
   * 盘点任务ID
   */
  taskId?: number | string;

  /**
   * 资产ID
   */
  assetId?: number | string;

  /**
   * 物联二维码
   */
  qrCode?: string;

  /**
   * 固定资产编码
   */
  assetCode?: string;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 使用科室
   */
  useDept?: string;

  /**
   * 使用地址
   */
  useLocal?: string;

  /**
   * 盘点状态
   */
  inventoryStatus?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface InventoryDetailQuery extends PageQuery {
  /**
   * 盘点任务ID
   */
  taskId?: number | string;
  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 使用科室
   */
  useDept?: string;

  /**
   * 盘点状态
   */
  inventoryStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
