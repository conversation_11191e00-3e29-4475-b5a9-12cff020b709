import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';


export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'assetId',
    label: '资产ID',
  },
  {
    component: 'Input',
    fieldName: 'assetCode',
    label: '资产编码',
  },
  {
    component: 'Input',
    fieldName: 'deptId',
    label: '转出科室',
  },
  {
    component: 'Input',
    fieldName: 'assetName',
    label: '资产名称',
  },
  {
    component: 'Input',
    fieldName: 'brand',
    label: '品牌',
  },
  {
    component: 'Input',
    fieldName: 'model',
    label: '型号',
  },
  {
    component: 'Input',
    fieldName: 'serialNumber',
    label: '	出厂编码',
  },
  {
    component: 'Input',
    fieldName: 'manageDept',
    label: '管理部门',
  },
  {
    component: 'Input',
    fieldName: 'applicant',
    label: '申请人',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'applicationDate',
    label: '申请日期',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
    },
    fieldName: 'status',
    label: '状态',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '退库ID',
    field: 'retireId',
  },
  {
    title: '资产ID',
    field: 'assetId',
  },
  {
    title: '资产编码',
    field: 'assetCode',
  },
  {
    title: '转出科室',
    field: 'deptId',
  },
  {
    title: '资产名称',
    field: 'assetName',
  },
  {
    title: '品牌',
    field: 'brand',
  },
  {
    title: '型号',
    field: 'model',
  },
  {
    title: '	出厂编码',
    field: 'serialNumber',
  },
  {
    title: '管理部门',
    field: 'manageDept',
  },
  {
    title: '申请人',
    field: 'applicant',
  },
  {
    title: '申请日期',
    field: 'applicationDate',
  },
  {
    title: '状态',
    field: 'status',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '退库ID',
    fieldName: 'retireId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '资产ID',
    fieldName: 'assetId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '资产编码',
    fieldName: 'assetCode',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '转出科室',
    fieldName: 'deptId',
    component: 'Input',
  },
  {
    label: '资产名称',
    fieldName: 'assetName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '品牌',
    fieldName: 'brand',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '型号',
    fieldName: 'model',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '	出厂编码',
    fieldName: 'serialNumber',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '管理部门',
    fieldName: 'manageDept',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '申请人',
    fieldName: 'applicant',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '申请日期',
    fieldName: 'applicationDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    rules: 'required',
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
    },
    rules: 'selectRequired',
  },
];
