import type { PageQuery, BaseEntity } from '#/api/common';

export interface AssetRetireVO {
  /**
   * 退库ID
   */
  retireId: string | number;

  /**
   * 资产ID
   */
  assetId: string | number;

  /**
   * 资产编码
   */
  assetCode: string;

  /**
   * 转出科室
   */
  deptId: string | number;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 品牌
   */
  brand: string;

  /**
   * 型号
   */
  model: string;

  /**
   * 	出厂编码
   */
  serialNumber: string;

  /**
   * 管理部门
   */
  manageDept: number;

  /**
   * 申请人
   */
  applicant: number;

  /**
   * 申请日期
   */
  applicationDate: string;

  /**
   * 状态
   */
  status: string;

}

export interface AssetRetireForm extends BaseEntity {
  /**
   * 退库ID
   */
  retireId?: string | number;

  /**
   * 资产ID
   */
  assetId?: string | number;

  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 转出科室
   */
  deptId?: string | number;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 型号
   */
  model?: string;

  /**
   * 	出厂编码
   */
  serialNumber?: string;

  /**
   * 管理部门
   */
  manageDept?: number;

  /**
   * 申请人
   */
  applicant?: number;

  /**
   * 申请日期
   */
  applicationDate?: string;

  /**
   * 状态
   */
  status?: string;

}

export interface AssetRetireQuery extends PageQuery {
  /**
   * 资产ID
   */
  assetId?: string | number;

  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 转出科室
   */
  deptId?: string | number;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 型号
   */
  model?: string;

  /**
   * 	出厂编码
   */
  serialNumber?: string;

  /**
   * 管理部门
   */
  manageDept?: number;

  /**
   * 申请人
   */
  applicant?: number;

  /**
   * 申请日期
   */
  applicationDate?: string;

  /**
   * 状态
   */
  status?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
