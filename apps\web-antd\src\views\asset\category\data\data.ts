import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    fieldName: 'categoryLabel',
    label: '分类名称',
    component: 'Input',
  },
  {
    fieldName: 'categoryValue',
    label: '分类编码',
    component: 'Input',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'seq', width: 70 },
  {
    field: 'categoryLabel',
    title: '分类名称',
    treeNode: true,
  },
  {
    field: 'categoryValue',
    title: '分类编码',
  },
  {
    field: 'orderNum',
    title: '排序',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 200,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    label: 'dataId',
    fieldName: 'dataId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    fieldName: 'parentId',
    label: '父级分类',
    rules: 'required',
    component: 'TreeSelect',
  },
  {
    fieldName: 'categoryLabel',
    label: '分类名称',
    component: 'Input',
    rules: 'required',
  },
  {
    fieldName: 'categoryValue',
    label: '分类编码',
    component: 'Input',
    rules: 'required',
  },
  {
    fieldName: 'categoryType',
    label: '分类类型',
    component: 'Input',
    rules: 'required',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    fieldName: 'orderNum',
    label: '排序',
    component: 'InputNumber',
    rules: 'required',
  },
];
