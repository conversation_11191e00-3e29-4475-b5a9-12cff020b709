<script setup lang="ts">
import type { TabOption } from '@vben/types';

import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AnalysisChartsTabs, Page } from '@vben/common-ui';
import { addFullName, cloneDeep, getPopupContainer } from '@vben/utils';

import {
  Button,
  Card,
  Col,
  message,
  Row,
  Timeline,
  TimelineItem,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { assetFinanceInfo } from '#/api/asset/assetFinance';
import { assetManagementInfo } from '#/api/asset/assetManagement';
import { assetProfileInfo, assetProfileUpdate, getDeptTree, assetProfileHisInfo, assetOtherInfo } from '#/api/asset/assetProfile';
import { categoryTree } from '#/api/asset/category/type';

import {
  baseSchema,
  entityEditSchema,
  financeSchema,
  historySchema,
  otherInfoSchema,
} from './data';

const emit = defineEmits<{ reload: [] }>();

const route = useRoute();
const router = useRouter();

// 获取资产ID
const assetId = ref(route.query.id as string);
const financeId = ref(route.query.financeId as string);
const managementId = ref(route.query.managementId as string);
const assetName = ref('');
const assetCode = ref('');
const isLoading = ref(false);

// 部门树数据
const deptTree = ref<any[]>([]);

// 医疗器械分类树数据
const medicalDeviceCategoryTree = ref<any[]>([]);

// 安装位置树数据
const installLocationTree = ref<any[]>([]);

// 固定资产分类树数据
const fixedAssetCodeTree = ref<any[]>([]);

const [BaseForm, baseApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: baseSchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});

const [FinanceForm, financeApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: financeSchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});

const [EntityForm, entityformApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: entityEditSchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});

const [OtherInfoForm, otherInfoApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: otherInfoSchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});

const [HistoryForm, hisApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: historySchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});
/**
 * 初始化部门选择
 */
async function setupDeptSelect() {
  try {
    // 获取部门树数据
    const deptTreeData = await getDeptTree();
    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(deptTreeData, 'label', ' / ');
    deptTree.value = deptTreeData;

    // 更新实体表单中的部门选择字段
    entityformApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择使用科室',
          showSearch: true,
          treeData: deptTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
          disabled: true, // 编辑页面中科室选择是禁用的
        }),
        fieldName: 'deptSection',
        label: '使用科室',
      },
    ]);

    // 更新实体表单中的管理科室字段
    entityformApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: {
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择管理科室',
          showSearch: true,
          treeData: deptTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
        },
        fieldName: 'manageDept',
        label: '管理科室',
      },
    ]);

    // 更新实体历史表单中的部门选择字段
    hisApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择使用科室',
          showSearch: true,
          treeData: deptTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
          disabled: true,
        }),
        fieldName: 'deptSection',
        label: '使用科室',
      },
    ]);

  } catch (error) {
    console.error('初始化部门选择失败:', error);
  }
}

// 加载资产数据
async function loadAssetData() {
  if (!assetId.value || !financeId.value || !managementId.value) {
    message.error('缺少ID参数');
    router.push('/asset/assetProfile');
    return;
  }

  try {
    isLoading.value = true;
    const entityData = await assetManagementInfo(managementId.value);
    const assetData = await assetProfileInfo(assetId.value);
    const hisData = await assetProfileHisInfo(assetId.value);
    const financeData = await assetFinanceInfo(financeId.value);
    const OtherInfoData = await assetOtherInfo(assetId.value);
    assetName.value = assetData.assetName;
    assetCode.value = assetData.assetCode;

    // 设置基本信息（直接使用返回的数据，因为API可能返回扁平化的数据）
    await baseApi.setValues(assetData);

    // 设置财务信息（如果有单独的财务数据字段）
    await financeApi.setValues(financeData);
    await entityformApi.setValues(entityData);
    await hisApi.setValues(hisData);
    await otherInfoApi.setValues(OtherInfoData);

    // 重新更新TreeSelect配置，确保回显正常
    if (fixedAssetCodeTree.value.length > 0) {
      baseApi.updateSchema([
        {
          component: 'TreeSelect',
          componentProps: () => ({
            class: 'w-full',
            fieldNames: {
              key: 'id',
              value: 'id',
              children: 'children',
              label: 'label',
            },
            getPopupContainer,
            placeholder: '请选择固定资产分类',
            showSearch: true,
            treeData: fixedAssetCodeTree.value,
            treeDefaultExpandAll: true,
            treeLine: { showLeafIcon: false },
            // 筛选的字段
            treeNodeFilterProp: 'label',
            // 选中后显示在输入框的值
            treeNodeLabelProp: 'fullName',
            // 确保回显正确显示
            displayRender: (labels: string[], selectedOptions: any[]) => {
              if (selectedOptions && selectedOptions.length > 0) {
                const lastOption = selectedOptions[selectedOptions.length - 1];
                return lastOption?.fullName || labels.join(' / ');
              }
              return labels.join(' / ');
            },
          }),
          fieldName: 'fixedAssetCode',
          label: '固定资产分类',
        },
      ]);
    }
  } catch (error) {
    console.error('加载资产数据失败:', error);
    message.error('加载资产数据失败');
  } finally {
    isLoading.value = false;
  }
}

/**
 * 初始化医疗器械分类选择
 */
async function setupMedicalDeviceCategorySelect() {
  try {
    // 获取医疗器械分类树数据
    const categoryData = await categoryTree('ast_medical_device_category');
    console.log('医疗器械分类树数据:', categoryData);

    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(categoryData, 'label', ' / ');
    medicalDeviceCategoryTree.value = categoryData;

    // 更新基础表单中的医疗器械分类字段
    baseApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择医疗器械分类',
          showSearch: true,
          treeData: medicalDeviceCategoryTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
          // 确保回显正确显示
          displayRender: (labels: string[], selectedOptions: any[]) => {
            if (selectedOptions && selectedOptions.length > 0) {
              const lastOption = selectedOptions[selectedOptions.length - 1];
              return lastOption?.fullName || labels.join(' / ');
            }
            return labels.join(' / ');
          },
        }),
        fieldName: 'medicalDeviceCategory',
        label: '医疗器械分类',
      },
    ]);
  } catch (error) {
    console.error('初始化医疗器械分类选择失败:', error);
  }
}

/**
 * 初始化安装位置选择
 */
async function setupInstallLocationSelect() {
  try {
    // 获取安装位置树数据
    const locationData = await categoryTree('ast_location');
    console.log('安装位置树数据:', locationData);

    if (!locationData || locationData.length === 0) {
      console.warn('安装位置数据为空');
      return;
    }

    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(locationData, 'label', ' / ');
    installLocationTree.value = locationData;

    console.log('处理后的安装位置树数据:', installLocationTree.value);

    // 更新实体表单中的安装位置字段
    entityformApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择安装位置',
          showSearch: true,
          treeData: installLocationTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
          // 确保回显正确显示
          displayRender: (labels: string[], selectedOptions: any[]) => {
            if (selectedOptions && selectedOptions.length > 0) {
              const lastOption = selectedOptions[selectedOptions.length - 1];
              return lastOption?.fullName || labels.join(' / ');
            }
            return labels.join(' / ');
          },
        }),
        fieldName: 'installationLocation',
        label: '安装位置',
      },
    ]);
  } catch (error) {
    console.error('初始化安装位置选择失败:', error);
  }
}

/**
 * 初始化固定资产分类选择
 */
async function setupFixedAssetCodeSelect() {
  try {
    // 获取固定资产分类树数据
    const categoryData = await categoryTree('ast_state_asset_category');
    console.log('固定资产分类树数据:', categoryData);

    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(categoryData, 'label', ' / ');
    fixedAssetCodeTree.value = categoryData;

    // 更新基础表单中的固定资产分类字段
    baseApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择固定资产分类',
          showSearch: true,
          treeData: fixedAssetCodeTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
          // 确保回显正确显示
          displayRender: (labels: string[], selectedOptions: any[]) => {
            if (selectedOptions && selectedOptions.length > 0) {
              const lastOption = selectedOptions[selectedOptions.length - 1];
              return lastOption?.fullName || labels.join(' / ');
            }
            return labels.join(' / ');
          },
        }),
        fieldName: 'fixedAssetCode',
        label: '固定资产分类',
      },
    ]);
  } catch (error) {
    console.error('初始化固定资产分类选择失败:', error);
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await setupDeptSelect(); // 先初始化部门选择
  await setupMedicalDeviceCategorySelect(); // 初始化医疗器械分类选择
  await setupInstallLocationSelect(); // 初始化安装位置选择
  await setupFixedAssetCodeSelect(); // 初始化固定资产分类选择
  await loadAssetData(); // 再加载资产数据
});

async function onSubmit(status: string) {
  baseApi.setFieldValue('status', status);
  // 验证所有表单
  const [baseValid, financeValid, entityValid] = await Promise.all([
    baseApi.validate(),
    financeApi.validate(),
    entityformApi.validate(),
  ]);

  if (!baseValid.valid || !financeValid.valid || !entityValid.valid) {
    message.error('请检查表单填写是否正确');
    return;
  }

  try {
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const baseData = cloneDeep(await baseApi.getValues());
    const financeData = cloneDeep(await financeApi.getValues());
    const entityData = cloneDeep(await entityformApi.getValues());
    const otherData = cloneDeep(await otherInfoApi.getValues());

    // 合并三个表单的JSON数据
    const allFormData: any = {
      assetId: assetId.value, // 添加资产ID用于更新
      profile: baseData,
      finance: financeData,
      entity: entityData,
      other: otherData,
    };

    console.log('合并后的JSON数据:', allFormData);

    // 更新数据
    await assetProfileUpdate(allFormData);

    message.success('资产更新成功');
    emit('reload');
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败，请重试');
  }
}

const chartTabs: TabOption[] = [
  {
    label: '基本信息',
    value: 'baseInfo',
  },
  {
    label: '附件资料',
    value: 'attachment',
  },
  {
    label: '配件信息',
    value: 'accessoriesInfo',
  },
  {
    label: '维修记录',
    value: 'maintenanceRecord',
  },
  {
    label: '转科记录',
    value: 'transferRecord',
  },
  {
    label: '位置变更记录',
    value: 'locationChangeRecord',
  },
  {
    label: '财政信息',
    value: 'financialInformation',
  },
  {
    label: '续保记录',
    value: 'renewalRecord',
  },
  {
    label: '计量数据',
    value: 'measurementRecord',
  },
  {
    label: '巡检记录',
    value: 'inspectionRecord',
  },
  {
    label: 'PM记录',
    value: 'pmRecord',
  },
  {
    label: '培训记录',
    value: 'trainingRecord',
  },
  {
    label: '不良事件',
    value: 'adverseRecord',
  },
];
</script>

<template>
  <Page>
    <AnalysisChartsTabs :tabs="chartTabs">
      <template #baseInfo>
        <Row :gutter="[15, 15]">
          <Col :span="24">
          <Card>
            <template #title>
              <span style="font-size: 20px">
                {{ assetName }}——资产编码：
                {{ assetCode }}
              </span>
            </template>
            <template #extra>
              <Button @click="router.push('/asset/assetProfile')">
                返回列表
              </Button>
              <Button type="primary" class="ml-10" @click="onSubmit('1')" :loading="isLoading">
                保存修改
              </Button>
            </template>
          </Card>
          </Col>
          <Col :span="24">
          <Card>
            <template #title>
              <span style="font-size: 20px">管理信息</span>
            </template>
            <el-divider />
            <EntityForm />
          </Card>
          </Col>

          <Col :span="24">
          <Card>
            <template #title>
              <span style="font-size: 20px">基本信息</span>
            </template>
            <el-divider />
            <BaseForm />
          </Card>
          </Col>
          <Col :span="24">
          <Card>
            <template #title>
              <span style="font-size: 20px">财务信息</span>
            </template>
            <el-divider />
            <FinanceForm />
          </Card>
          </Col>

          <Col :span="24">
          <Card>
            <template #title>
              <span style="font-size: 20px">其他信息</span>
            </template>
            <el-divider />
            <OtherInfoForm />
          </Card>
          </Col>
          <Col :span="24">
          <Card>
            <template #title>
              <span style="font-size: 20px">生命周期</span>
            </template>
            <el-divider />
            <Timeline>
              <TimelineItem color="green">
                <span style="font-size: 20px; font-weight: bold">
                  验收信息
                </span>
                <br />
                <span>设备验收完成，符合采购要求</span> <br />
                <span>时间：2024-01-15 14:30:00</span><br />
                <span>责任人：张三</span>
              </TimelineItem>
              <TimelineItem color="blue">
                <span style="font-size: 20px; font-weight: bold">
                  入库信息
                </span>
                <br />
                <span>设备已入库，分配库位号A-001</span> <br />
                <span>时间：2024-01-16 09:00:00</span><br />
                <span>责任人：李四</span>
              </TimelineItem>
              <TimelineItem color="yellow">
                <span style="font-size: 20px; font-weight: bold">
                  出库信息
                </span>
                <br />
                <span>设备出库至心内科201病房</span> <br />
                <span>时间：2024-01-20 10:30:00</span><br />
                <span>责任人：王五</span>
              </TimelineItem>
              <TimelineItem color="purple">
                <span style="font-size: 20px; font-weight: bold">
                  设备维修
                </span>
                <br />
                <span>定期保养维护，更换传感器</span> <br />
                <span>时间：2024-06-15 15:00:00</span><br />
                <span>责任人：赵六</span>
              </TimelineItem>
            </Timeline>
          </Card>
          </Col>
          <Col :span="24">
          <Card>
            <template #title>
              <span style="font-size: 20px">历史台账信息</span>
            </template>
            <el-divider />
            <HistoryForm />
          </Card>
          </Col>
        </Row>
      </template>
      <template #attachment> 附件资料开发中... </template>
      <template #accessoriesInfo> 配件信息开发中... </template>
      <template #maintenanceRecord> 维修记录开发中... </template>
      <template #transferRecord> 转科记录开发中... </template>
      <template #locationChangeRecord> 位置变更记录开发中... </template>
      <template #financialInformation> 财政信息开发中... </template>
      <template #renewalRecord> 续保记录开发中... </template>
      <template #measurementRecord> 计量数据开发中... </template>
      <template #inspectionRecord> 巡检记录开发中... </template>
      <template #pmRecord> PM记录开发中... </template>
      <template #trainingRecord> 培训记录开发中... </template>
      <template #adverseRecord> 不良事件开发中... </template>
    </AnalysisChartsTabs>
  </Page>
</template>
