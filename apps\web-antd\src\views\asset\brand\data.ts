import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'nameZh',
    label: '中文全称',
  },
  {
    component: 'Input',
    fieldName: 'nameEn',
    label: '英文全称',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '品牌ID',
    field: 'brandId',
  },
  {
    title: '中文全称',
    field: 'nameZh',
  },
  {
    title: '英文全称',
    field: 'nameEn',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    label: '品牌ID',
    fieldName: 'brandId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '中文全称',
    fieldName: 'nameZh',
    component: 'Input',
  },
  {
    label: '英文全称',
    fieldName: 'nameEn',
    component: 'Input',
  },
];
