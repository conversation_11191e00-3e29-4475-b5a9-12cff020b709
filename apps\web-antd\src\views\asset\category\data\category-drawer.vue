<script setup lang="ts">
import type { CategoryVO } from '#/api/asset/category/data/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import {
  addFullName,
  cloneDeep,
  getPopupContainer,
  listToTree,
} from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import {
  categoryAdd,
  categoryInfo,
  categoryList,
  categoryUpdate,
} from '#/api/asset/category/data';

import { drawerSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 80,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: drawerSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

async function setupCategorySelect(categoryType?: string) {
  const listData = await categoryList({ categoryType });
  appendValue2Label(listData);
  const treeData = listToTree(listData, {
    id: 'dataId',
    pid: 'parentId',
  });
  const fullCategoryTree = [
    {
      dataId: 0,
      categoryLabel: $t('menu.root'),
      children: treeData,
    },
  ];
  addFullName(fullCategoryTree, 'categoryLabel', ' / ');
  formApi.updateSchema([
    {
      fieldName: 'parentId',
      componentProps: {
        treeData: fullCategoryTree,
        treeLine: { showLeafIcon: false },
        showSearch: true,
        fieldNames: { label: 'categoryLabel', value: 'dataId' },
        treeDefaultExpandAll: true,
        // 筛选的字段
        treeNodeFilterProp: 'categoryLabel',
        treeNodeLabelProp: 'fullName',
        getPopupContainer,
      },
    },
  ]);
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    const { id, parentId, categoryType } = drawerApi.getData() as {
      categoryType?: string;
      id?: number | string;
      parentId?: number | string;
    };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await categoryInfo(id);
      await formApi.setValues(record);
    }
    if (parentId) {
      await formApi.setValues({ parentId });
    }
    if (categoryType) {
      await formApi.setValues({ categoryType });
    }
    await setupCategorySelect(categoryType);

    drawerApi.drawerLoading(false);
  },
});

// 完整的label
function appendValue2Label(listData: CategoryVO[]) {
  listData.forEach((item) => {
    item.categoryLabel = `${item.categoryLabel}(${item.categoryValue})`;
  });
}

async function handleConfirm() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());
    await (isUpdate.value ? categoryUpdate(data) : categoryAdd(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[800px]">
    <BasicForm />
  </BasicDrawer>
</template>
