import type { BaseEntity, PageQuery } from '#/api/common';

export interface AssetProfileVO {
  /**
   * 资产ID
   */
  assetId: number | string;

  /**
   * 资产编码
   */
  assetCode: string;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 通用名
   */
  commonName: string;

  /**
   * 资产类型
   */
  assetType: string;

  /**
   * 品牌
   */
  brand: string;

  /**
   * 型号
   */
  model: string;

  /**
   * 使用年限
   */
  validityPeriod: number | string;

  /**
   * 生产厂家
   */
  manufacturer: number;

  /**
   * 供应商
   */
  supplier: number;

  /**
   * 产地
   */
  originCountry: string;

  /**
   * 医疗器械注册/备案号
   */
  registrationNumber: string;

  /**
   * 计量单位
   */
  measurementUnit: string;

  /**
   * 固定资产分类代码
   */
  fixedAssetCode: string;

  /**
   * 专业分类
   */
  specialtyCategory: string;

  /**
   * 医疗器械分类
   */
  medicalDeviceCategory: string;

  /**
   * 资产类别
   */
  assetCategory: string;
}

export interface AssetProfileForm extends BaseEntity {
  /**
   * 资产ID
   */
  assetId?: number | string;

  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 通用名
   */
  commonName?: string;

  /**
   * 资产类型
   */
  assetType?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 型号
   */
  model?: string;

  /**
   * 使用年限
   */
  validityPeriod?: number | string;

  /**
   * 生产厂家
   */
  manufacturer?: number;

  /**
   * 供应商
   */
  supplier?: number;

  /**
   * 产地
   */
  originCountry?: string;

  /**
   * 医疗器械注册/备案号
   */
  registrationNumber?: string;

  /**
   * 计量单位
   */
  measurementUnit?: string;

  /**
   * 固定资产分类代码
   */
  fixedAssetCode?: string;

  /**
   * 专业分类
   */
  specialtyCategory?: string;

  /**
   * 医疗器械分类
   */
  medicalDeviceCategory?: string;

  /**
   * 资产类别
   */
  assetCategory?: string;
}

export interface AssetProfileQuery extends PageQuery {
  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 通用名
   */
  commonName?: string;

  /**
   * 资产类型
   */
  assetType?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 型号
   */
  model?: string;

  /**
   * 使用年限
   */
  validityPeriod?: number | string;

  /**
   * 生产厂家
   */
  manufacturer?: number;

  /**
   * 供应商
   */
  supplier?: number;

  /**
   * 产地
   */
  originCountry?: string;

  /**
   * 医疗器械注册/备案号
   */
  registrationNumber?: string;

  /**
   * 计量单位
   */
  measurementUnit?: string;

  /**
   * 固定资产分类代码
   */
  fixedAssetCode?: string;

  /**
   * 专业分类
   */
  specialtyCategory?: string;

  /**
   * 医疗器械分类
   */
  medicalDeviceCategory?: string;

  /**
   * 资产类别
   */
  assetCategory?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
/**
 * @description: 部门树
 */
export interface DeptTree {
  id: number;
  /**
   * antd组件必须要这个属性 实际是没有这个属性的
   */
  key: string;
  parentId: number;
  label: string;
  weight: number;
  children?: DeptTree[];
}

export interface AssetProfileHisVO {
  /**
   * 资产ID
   */
  assetId: string | number;

  /**
   * 资产编码
   */
  assetCode: string;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 型号
   */
  model: string;

  /**
   * 管理信息ID
   */
  managementId: string | number;

  /**
   * 序列号
   */
  serialNumber: string;

  /**
   * 所属科室
   */
  deptSection: number;

}

export interface AssetOtherVO {
  /**
   * 资产ID
   */
  assetId: string | number;

  /**
   * 物联标签类型
   */
  tagType: string;

  /**
   * 计量标志
   */
  measurementMark: string;

  /**
   * 质控标志
   */
  qualityControlMark: string;

  /**
   * PM标志
   */
  pmMark: string;

  /**
   * PM分类
   */
  pmCategory: string;

  /**
   * 记账日期
   */
  bookkeepingDate: string;

  /**
   * 记账凭证号
   */
  accountingVoucherNumber: string;

}

export interface AssetSummaryVO {
  /**
   * 资产ID
   */
  assetId: number | string;

  /**
   * 资产编码
   */
  assetCode: string;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 通用名
   */
  commonName: string;

  /**
   * 资产类型
   */
  assetType: string;

  /**
   * 品牌
   */
  brand: string;

  /**
   * 型号
   */
  model: string;

  /**
   * 使用年限
   */
  validityPeriod: number | string;

  /**
   * 生产厂家
   */
  manufacturer: number;

  /**
   * 供应商
   */
  supplier: number;

  /**
   * 产地
   */
  originCountry: string;

  /**
   * 医疗器械注册/备案号
   */
  registrationNumber: string;

  /**
   * 计量单位
   */
  measurementUnit: string;

  /**
   * 固定资产分类代码
   */
  fixedAssetCode: string;

  /**
   * 专业分类
   */
  specialtyCategory: string;

  /**
   * 医疗器械分类
   */
  medicalDeviceCategory: string;

  /**
   * 资产类别
   */
  assetCategory: string;

    /**
   * 财务信息ID
   */
  financeId: number | string;

  /**
   * 资产ID
   */
  assetId: number | string;

  /**
   * 原值(元)
   */
  contractPrice: number;

  /**
   * 启用日期
   */
  activationDate: string;

  /**
   * 折旧方法
   */
  depreciationMethod: string;

  /**
   * 折旧年限
   */
  depreciationYears: number;

  /**
   * 残值率(%)
   */
  residualRate: number | string;

  /**
   * 财政拨款
   */
  financeAppropriation: number;

  /**
   * 科研专项
   */
  researchProject: number;

  /**
   * 自筹资金
   */
  selfRaised: number;

    /**
   * 管理信息ID
   */
  managementId: number | string;

  /**
   * 资产ID
   */
  assetId: number | string;

  /**
   * 管理部门
   */
  manageDept: number;

  /**
   * 使用科室
   */
  deptSection: number;

  /**
   * 安装地点
   */
  installationLocation: string;

  /**
   * 资产管理员
   */
  assetManager: number;

  /**
   * 使用科室
   */
  useDept: number;

  /**
   * 保修开始日期
   */
  warrantyStartDate: string;

  /**
   * 保修截止日期
   */
  warrantyEndDate: string;

  /**
   * 标签二维码
   */
  qrCode: string;

  /**
   * 序列号
   */
  serialNumber: string;

  /**
   * 实物照片(多个ossId,逗号分隔)
   */
  itemPhotos: string;

  /**
   * 生产日期
   */
  productionDate: string;

  /**
   * 资产状态
   */
  assetStatus: string;

  /**
   * 使用状态
   */
  useStatus: string;
  
}
