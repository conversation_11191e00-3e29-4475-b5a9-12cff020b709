import type { FormSchemaGetter, VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { assetProfileList } from '#/api/asset/assetProfile';
import type { AssetProfileVO } from '#/api/asset/assetProfile/model';
import { getDataOptions, getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 获取字典数据
const brandOptions = getDataOptions('brand');
const deptTreeOptions = getDataOptions('deptTree');

/**
 * 从品牌选项中根据value查找对应的label
 * @param brandValue 品牌值
 * @returns 品牌名称，如果未找到则返回原值
 */
function findBrandLabelByValue(brandValue: string | number): string {
  if (!brandValue) return '';

  const options = brandOptions.value;
  if (!options || !Array.isArray(options)) return String(brandValue);

  const brandOption = options.find(option =>
    String(option.value) === String(brandValue)
  );

  return brandOption ? brandOption.label : String(brandValue);
}

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'assetCode',
    label: '资产编码',
  },
  {
    component: 'Input',
    fieldName: 'deptOutId',
    label: '转出科室',
  },
  {
    component: 'Input',
    fieldName: 'deptInId',
    label: '转出科室',
  },
  {
    component: 'Input',
    fieldName: 'assetName',
    label: '资产名称',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择品牌',
      options: brandOptions,
    },
    fieldName: 'brand',
    label: '品牌',
  },
  {
    component: 'Input',
    fieldName: 'model',
    label: '型号',
  },
  {
    component: 'Input',
    fieldName: 'serialNumber',
    label: '	出厂编码',
  },
  {
    component: 'Input',
    fieldName: 'manageDept',
    label: '管理部门',
  },
  {
    component: 'Input',
    fieldName: 'applicant',
    label: '申请人',
  },
  {
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'applicationDate',
    label: '申请日期',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '转科单号',
    field: 'transferId',
  },
  {
    title: '资产编码',
    field: 'assetCode',
  },
  {
    title: '转出科室',
    field: 'deptOutId',
  },
  {
    title: '转出科室',
    field: 'deptInId',
  },
  {
    title: '资产名称',
    field: 'assetName',
  },
  {
    title: '品牌',
    field: 'brand',
    formatter: ({ cellValue }) => {
      return findBrandLabelByValue(cellValue);
    },
  },
  {
    title: '型号',
    field: 'model',
  },
  {
    title: '出厂编码',
    field: 'serialNumber',
  },
  {
    title: '管理部门',
    field: 'manageDept',
  },
  {
    title: '申请人',
    field: 'applicant',
  },
  {
    title: '申请日期',
    field: 'applicationDate',
  },
  {
    title: '状态',
    field: 'status',
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, DictEnum.WF_BUSINESS_STATUS);
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 300,
  },
];

export const modalSchema = (isEdit: boolean = true) => [
  {
    label: '转科单号',
    fieldName: 'transferId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '资产编码',
    fieldName: 'assetId',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '资产编码',
    fieldName: 'assetCode',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    component: 'TreeSelect',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择转出科室',
      showSearch: true,
      treeData: deptTreeOptions,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
      disabled: true,
    },
    fieldName: 'deptOutId',
    label: '转出科室',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择转入科室',
      showSearch: true,
      treeData: deptTreeOptions,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
    fieldName: 'deptInId',
    label: '转入科室',
  },
  {
    label: '资产编码',
    fieldName: 'assetCode',
    component: 'Input',
    componentProps: {
      readonly: true,
      placeholder: '请选择资产',
      disabled: true,
    },
  },
  {
    label: '资产名称',
    fieldName: 'assetName',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: true,
      placeholder: '请选择资产',
    },
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择品牌',
      options: brandOptions,
      disabled: true,
    },
    fieldName: 'brand',
    label: '品牌',
  },
  {
    label: '型号',
    fieldName: 'model',
    component: 'Input',
    componentProps: {
      readonly: true,
      placeholder: '请选择型号',
      disabled: true,
    },
  },
  {
    label: '出厂编码',
    fieldName: 'serialNumber',
    component: 'Input',
    componentProps: {
      readonly: true,
      placeholder: '请选择资产',
      disabled: true,
    },
  },
  {
    label: '管理部门',
    fieldName: 'manageDept',
    component: 'TreeSelect',
    componentProps: {
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      disabled: true,
      placeholder: '请选择管理部门',
      showSearch: true,
      treeData: deptTreeOptions,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
  },
  {
    label: '申请人',
    fieldName: 'applicant',
    component: 'UserSelect',
    componentProps: {
      placeholder: '请选择申请人',
    },
    rules: 'required',
  },
  {
    label: '申请日期',
    fieldName: 'applicationDate',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    rules: 'required',
  },
];

/**
 * 资产选择表单配置
 */
export const assetSelectFormOptions: VbenFormProps = {
  schema: [
    {
      component: 'Input',
      fieldName: 'assetCode',
      label: '资产编码',
      componentProps: {
        placeholder: '请输入资产编码',
      },
    },
    {
      label: '资产编码',
      fieldName: 'assetId',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
    },
    {
      component: 'Input',
      fieldName: 'assetName',
      label: '资产名称',
      componentProps: {
        placeholder: '请输入资产名称',
      },
    },
    {
      component: 'Input',
      fieldName: 'commonName',
      label: '通用名',
      componentProps: {
        placeholder: '请输入通用名',
      },
    },
    {
      component: 'Input',
      fieldName: 'model',
      label: '型号',
      componentProps: {
        placeholder: '请输入型号',
      },
    },

  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
};

/**
 * 资产选择表格配置
 */
export const assetSelectGridOptions: VxeGridProps = {
  radioConfig: {
    highlight: true,
    reserve: false,
    trigger: 'row', // 点击行选中
  },
  // 启用双击事件
  rowConfig: {
    keyField: 'assetId',
    isHover: true,
  },
  columns: [
    { type: 'radio', width: 60, fixed: 'left', },
    {
      title: '资产编码',
      field: 'assetCode',
      width: 120,
      fixed: 'left',
    },
    {
      title: '资产名称',
      field: 'assetName',
      width: 150,
      fixed: 'left',
    },
    {
      title: '通用名',
      field: 'commonName',
      width: 120,
    },
    {
      title: '品牌',
      field: 'brand',
      width: 100,
      formatter: ({ cellValue }) => {
        return findBrandLabelByValue(cellValue);
      },
    },
    {
      title: '型号',
      field: 'model',
      width: 120,
    },
    {
      title: '资产类型',
      field: 'assetType',
      width: 100,
      slots: {
        default: ({ row }) => {
          return renderDict(row.assetType, DictEnum.ASSETS_TYPE);
        },
      },
    },
    {
      title: '生产厂家',
      field: 'manufacturer',
      width: 120,
    },
    {
      title: '供应商',
      field: 'supplier',
      width: 120,
    },
    {
      title: '产地',
      field: 'originCountry',
      width: 100,
    },
    {
      title: '计量单位',
      field: 'measurementUnit',
      width: 80,
    },
    {
      title: '使用年限',
      field: 'validityPeriod',
      width: 80,
      formatter: ({ cellValue }) => {
        return cellValue ? `${cellValue}年` : '-';
      },
    },
    {
      title: '固定资产分类',
      field: 'fixedAssetCode',
      width: 120,
    },
    {
      title: '专业分类',
      field: 'specialtyCategory',
      slots: {
        default: ({ row }) => {
          return renderDict(row.assetType, DictEnum.SPECIALTY_CATEGORY);
        },
      },
      width: 100,
    },
    {
      title: '医疗器械分类',
      field: 'medicalDeviceCategory',
      width: 120,
    },
    {
      title: '资产类别',
      field: 'assetCategory',
      slots: {
        default: ({ row }) => {
          return renderDict(row.assetType, DictEnum.ASSETS_CATEGORY);
        },
      },
      width: 100,
    },
    {
      title: '医疗器械注册号',
      field: 'registrationNumber',
      width: 150,
    },
  ],
  height: 400,
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await assetProfileList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};

/**
 * 创建带有双击事件的资产选择表格事件配置
 */
export function createAssetSelectGridEvents(onRowDblClick: (params: any) => void) {
  return {
    cellDblclick: onRowDblClick,
  };
}
