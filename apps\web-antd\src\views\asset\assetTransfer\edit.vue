<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { But<PERSON>, Card, Col, message, Row, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useVbenModal } from '@vben/common-ui';
import { assetTransferAdd, assetTransferInfo, assetTransferUpdate } from '#/api/asset/assetTransfer';
import type { AssetProfileVO } from '#/api/asset/assetProfile/model';

import AssetDetailModalComponent from './asset-detail-modal.vue';
import AssetTransferApplyModalComponent from './asset-transfer-apply-modal.vue';
import TransferDescription from './transfer-description.vue';
import { assetSelectFormOptions, assetSelectGridOptions, createAssetSelectGridEvents, modalSchema } from './data';
import type { StartWorkFlowReqData } from '#/api/workflow/task/model';
import { startWorkFlow } from '#/api/workflow/task';
import type { AssetTransferVO } from '#/api/asset/assetTransfer/model';

const emit = defineEmits<{ reload: [] }>();

const route = useRoute();
const router = useRouter();

// 获取路由参数
const transferId = ref(route.query.id as string);
const readonly = route.query?.readonly === 'true';
const isLoading = ref(false);

// 计算属性
const isUpdate = computed(() => !!transferId.value);
const showActionBtn = computed(() => !readonly);
const title = computed(() => {
  if (readonly) return '查看资产调拨';
  return isUpdate.value ? '编辑资产调拨' : '新增资产调拨';
});

// 显示详情时需要较小的padding
const cardSize = computed(() => {
  return showDescription.value ? 'small' : 'default';
});



// 选中的资产
const selectedAsset = ref<AssetProfileVO | null>(null);

// 显示描述信息的状态
const showDescription = ref(false);
const transferDescription = ref<AssetTransferVO | null>(null);

const [AssetTransferForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 120,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
      disabled: readonly, // 只读模式下禁用所有表单项
    }
  },
  schema: modalSchema(!readonly), // 传递编辑模式参数
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
});

// 处理双击查看资产详情
function handleAssetRowDblClick(params: any) {
  const { row } = params;
  assetDetailModalApi.setData({ assetId: row.assetId });
  assetDetailModalApi.open();
}

const [AssetSelectTable, assetTableApi] = useVbenVxeGrid({
  formOptions: assetSelectFormOptions,
  gridOptions: assetSelectGridOptions,
  gridEvents: {
    ...createAssetSelectGridEvents(handleAssetRowDblClick),
    radioChange: handleAssetSelect, // 添加 radio 选择事件
  },
});

// 资产详情模态框
const [AssetDetailModal, assetDetailModalApi] = useVbenModal({
  connectedComponent: AssetDetailModalComponent,
});

// 资产调拨流程申请模态框
const [AssetTransferApplyModal, assetTransferApplyModalApi] = useVbenModal({
  connectedComponent: AssetTransferApplyModalComponent,
});

// 加载数据
async function loadData() {
  if (!isUpdate.value || !transferId.value) {
    return;
  }

  try {
    isLoading.value = true;
    const record = await assetTransferInfo(transferId.value);

    // 如果是只读模式，显示描述信息
    if (readonly) {
      showDescription.value = true;
      transferDescription.value = record;
    } else {
      await formApi.setValues(record);
    }

    // 如果有资产信息，设置选中的资产
    if (record.assetId && record.assetCode) {
      selectedAsset.value = {
        assetId: record.assetId,
        assetCode: record.assetCode,
        assetName: record.assetName,
        brand: record.brand,
        model: record.model,
        serialNumber: record.serialNumber,
        manageDept: record.manageDept,
        useDept: record.deptOutId,
      } as unknown as AssetProfileVO;
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败');
  } finally {
    isLoading.value = false;
  }
}

// 保存数据
async function handleSave() {
  try {
    isLoading.value = true;
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请检查表单填写是否正确');
      return;
    }

    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());

    let result;
    if (isUpdate.value) {
      await assetTransferUpdate(data);
      message.success('更新成功');
      result = data; // 更新时返回原数据
    } else {
      result = await assetTransferAdd(data);
      message.success('新增成功');
    }

    emit('reload');
    handleBack();
    return result;
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
    throw error;
  } finally {
    isLoading.value = false;
  }
}
/**
 * 保存业务 & 发起流程
 */
async function handleStartWorkFlow() {
  try {
    // 保存业务（不跳转）
    const resp = await handleSaveForWorkflow();
    console.log('保存业务成功:', resp);
    // 启动流程
    const taskVariables = {
    };
    const flowCode = 'asset_transfer';
    const startWorkFlowData: StartWorkFlowReqData = {
      businessId: resp!.transferId,
      flowCode,
      variables: taskVariables,
    };
    const { taskId } = await startWorkFlow(startWorkFlowData);
    console.log('启动流程成功:', taskId);

    // 打开资产调拨流程申请弹窗
    assetTransferApplyModalApi.setData({
      taskId,
      taskVariables,
      variables: {
        deptOutId: resp!.deptOutId,
        deptInId: resp!.deptInId,
      },
    });
    assetTransferApplyModalApi.open();
  } catch (error) {
    console.error(error);
  }
}

// 专门为启动流程使用的保存函数（不跳转页面）
async function handleSaveForWorkflow() {
  try {
    isLoading.value = true;
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请检查表单填写是否正确');
      return;
    }

    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());

    let result;
    if (isUpdate.value) {
      await assetTransferUpdate(data);
      message.success('更新成功');
      result = data; // 更新时返回原数据
    } else {
      result = await assetTransferAdd(data);
      message.success('新增成功');
    }

    emit('reload');
    return result;
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
    throw error;
  } finally {
    isLoading.value = false;
  }
}
// 返回列表
function handleBack() {
  router.push('/asset/assetTransfer');
}

// 流程完成处理
function handleComplete() {
  formApi.resetForm();
  router.push('/asset/assetTransfer');
}

// 处理资产选择
function handleAssetSelect(params: any) {
  // VXE Table 的 radio-change 事件参数结构
  let row: AssetProfileVO;

  if (params.row) {
    row = params.row as AssetProfileVO;
  } else if (params && typeof params === 'object' && params.assetId) {
    // 如果参数直接是行数据
    row = params as AssetProfileVO;
  } else {
    return;
  }
  selectedAsset.value = row;

  // 自动填充表单字段 - 只同步表单中存在的字段
  const formData: Record<string, any> = {};

  // 定义字段映射关系：表单字段名 -> 资产数据字段名
  const fieldMapping = {
    assetCode: 'assetCode',
    assetName: 'assetName',
    brand: 'brand',
    model: 'model',
    serialNumber: 'serialNumber',
    manageDept: 'manageDept',
    deptOutId: 'useDept',
    assetId: 'assetId',
  };

  // 遍历映射关系，同步有值的字段
  Object.entries(fieldMapping).forEach(([formField, assetField]) => {
    const value = row[assetField as keyof AssetProfileVO];
    if (value !== undefined && value !== null && value !== '') {
      formData[formField] = value;
    }
  });



  // 批量设置表单值
  formApi.setValues(formData);

  // 用户提示
  const syncedFields = Object.keys(formData);
  if (syncedFields.length > 0) {
    message.success(`已自动填充 ${syncedFields.length} 个字段：${syncedFields.join('、')}`);
  }
}

// 查询资产列表
function handleAssetQuery() {
  assetTableApi.query();
}

// 重置资产查询
function handleAssetReset() {
  assetTableApi.formApi.resetForm();
  assetTableApi.query();
}

// 清除资产选择
function handleClearAssetSelection() {
  console.log(formApi.getValues());
  selectedAsset.value = null;
  // 清除相关表单字段
  formApi.setValues({
    assetCode: '',
    assetName: '',
    brand: '',
    model: '',
    serialNumber: '',
    manageDept: '',
    deptOutId: '',
    assetId: '',
  });
  message.info('已清除资产选择');
  console.log(formApi.getValues());
}





// 组件挂载时的初始化
onMounted(async () => {
  // 如果是编辑或查看模式，加载数据
  if (isUpdate.value || readonly) {
    await loadData();
  } else {
    // 新增模式，初始化表单数据
    formApi.setValues({
      applicationDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
    });
  }


});

</script>

<template>
  <Page>
    <Row :gutter="[15, 15]">
      <Col :span="24">
      <Card :size="cardSize">
        <template #title>
          <span style="font-size: 20px">{{ title }}</span>
        </template>
        <template #extra>
          <Space>
            <Button @click="handleBack">
              返回
            </Button>
            <Button v-if="showActionBtn" type="primary" :loading="isLoading" @click="handleSave">
              {{ isUpdate ? '更新' : '保存' }}
            </Button>
            <Button v-if="showActionBtn" type="primary" :loading="isLoading" @click="handleStartWorkFlow">
              提交
            </Button>
          </Space>
        </template>
        <!-- 使用v-show会影响生命周期 -->
        <AssetTransferForm v-show="!showDescription" />
        <TransferDescription v-if="showDescription" :data="transferDescription!" />
      </Card>
      </Col>

      <!-- 资产选择列表 - 只在非只读模式下显示 -->
      <Col v-if="!readonly" :span="24">
      <Card>
        <template #title>
          <span>选择资产</span>
          <span v-if="selectedAsset" style="color: #1890ff; margin-left: 10px;">
            （已选择：{{ selectedAsset.assetCode }} - {{ selectedAsset.assetName }}）
          </span>
        </template>
        <template #extra>
          <span style="color: #666; font-size: 12px;">提示：双击行可查看资产详情</span>
        </template>
        <AssetSelectTable>
          <template #toolbar-actions>
            <Space>
              <Button type="primary" @click="handleAssetQuery">查询</Button>
              <Button @click="handleAssetReset">重置</Button>
              <Button v-if="selectedAsset" type="default" danger @click="handleClearAssetSelection">
                清除选择
              </Button>
            </Space>
          </template>
        </AssetSelectTable>
      </Card>
      </Col>
    </Row>

    <!-- 资产详情模态框 -->
    <AssetDetailModal />

    <!-- 资产调拨流程申请模态框 -->
    <AssetTransferApplyModal @complete="handleComplete" />
  </Page>
</template>
