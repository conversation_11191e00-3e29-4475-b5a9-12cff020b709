import type {
  InventoryTaskForm,
  InventoryTaskQuery,
  InventoryTaskVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询资产盘点任务列表
 * @param params
 * @returns 资产盘点任务列表
 */
export function inventoryTaskList(params?: InventoryTaskQuery) {
  return requestClient.get<PageResult<InventoryTaskVO>>(
    '/asset/inventoryTask/list',
    { params },
  );
}

/**
 * 导出资产盘点任务列表
 * @param params
 * @returns 资产盘点任务列表
 */
export function inventoryTaskExport(params?: InventoryTaskQuery) {
  return commonExport('/asset/inventoryTask/export', params ?? {});
}

/**
 * 查询资产盘点任务详情
 * @param taskId id
 * @returns 资产盘点任务详情
 */
export function inventoryTaskInfo(taskId: ID) {
  return requestClient.get<InventoryTaskVO>(`/asset/inventoryTask/${taskId}`);
}

/**
 * 新增资产盘点任务
 * @param data
 * @returns void
 */
export function inventoryTaskAdd(data: InventoryTaskForm) {
  return requestClient.postWithMsg<void>('/asset/inventoryTask', data);
}

/**
 * 更新资产盘点任务
 * @param data
 * @returns void
 */
export function inventoryTaskUpdate(data: InventoryTaskForm) {
  return requestClient.putWithMsg<void>('/asset/inventoryTask', data);
}

/**
 * 删除资产盘点任务
 * @param taskId id
 * @returns void
 */
export function inventoryTaskRemove(taskId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/asset/inventoryTask/${taskId}`);
}

/**
 * 更新资产盘点任务状态
 * @param data
 * @returns void
 */
export function inventoryTaskChangeStatus(data: InventoryTaskForm) {
  return requestClient.putWithMsg<void>(
    '/asset/inventoryTask/changeStatus',
    data,
  );
}
