import { ref, type Ref } from 'vue';

import { requestClient, UnauthorizedException } from '#/api/request';
import { dictDataInfo } from '#/api/system/dict/dict-data';
import { useDictStore } from '#/store/dict';
import { getDeptTree } from '#/api/asset/assetProfile';
import { categoryTree } from '#/api/asset/category/type';

/**
 * 抽取公共逻辑的基础方法
 * @param dictName 字典名称
 * @param dataGetter 获取字典数据的函数
 * @param formatNumber 是否格式化字典value为number类型
 * @returns 数据
 */
function fetchAndCacheDictData<T>(
  dictName: string,
  dataGetter: () => T[],
  formatNumber = false,
): T[] {
  const { dictRequestCache, setDictInfo } = useDictStore();
  // 有调用方决定如何获取数据
  const dataList = dataGetter();

  // 检查请求状态缓存
  if (dataList.length === 0 && !dictRequestCache.has(dictName)) {
    dictRequestCache.set(
      dictName,
      dictDataInfo(dictName)
        .then((resp) => {
          // 缓存到store 这样就不用重复获取了
          // 内部处理了push的逻辑 这里不用push
          setDictInfo(dictName, resp, formatNumber);
        })
        .catch((error) => {
          /**
           * 需要判断是否为401抛出的特定异常 401清除缓存
           * 其他error清除缓存会导致无限循环调用字典接口 则不做处理
           */
          if (error instanceof UnauthorizedException) {
            // 401时 移除字典缓存 下次登录重新获取
            dictRequestCache.delete(dictName);
          }
          // 其他不做处理
        })
        .finally(() => {
          // 移除请求状态缓存
          /**
           * 这里主要判断字典item为空的情况(无奈兼容 不给字典item本来就是错误用法)
           * 会导致if一直进入逻辑导致接口无限刷新
           * 在这里dictList为空时 不删除缓存
           */
          if (dataList.length > 0) {
            dictRequestCache.delete(dictName);
          }
        }),
    );
  }
  return dataList;
}

/**
 * 这里是提供给渲染标签使用的方法
 * @deprecated 使用getDictOptions代替 于下个版本删除
 * @param dictName 字典名称
 * @returns 字典信息
 */
export function getDict(dictName: string) {
  const { getDictOptions } = useDictStore();
  return fetchAndCacheDictData(dictName, () => getDictOptions(dictName));
}

/**
 * 一般是Select, Radio, Checkbox等组件使用
 * @param dictName 字典名称
 * @param formatNumber 是否格式化字典value为number类型
 * @returns Options数组
 */
export function getDictOptions(dictName: string, formatNumber = false) {
  const { getDictOptions } = useDictStore();
  return fetchAndCacheDictData(
    dictName,
    () => getDictOptions(dictName),
    formatNumber,
  );
}

/**
 * 查询品牌字典
 * @returns 品牌字典
 */
export function getBrandDic() {
  return requestClient.get<void>(`/asset/brand/getBrandDic`);
}

/**
 * 查询供应商字典
 * @returns 供应商字典
 */
export function getSupplierDic() {
  return requestClient.get<void>(`/asset/supplier/getSupplierDic`);
}

// 数据选项缓存
const dataOptionsCache = new Map<string, Ref<Array<{ label: string; value: any }>>>();
// 请求状态缓存
const dataRequestCache = new Map<string, Promise<any>>();

/**
 * 查询数据字典选项（带缓存）
 * @param dictName 字典名称，支持：'brand'(品牌)、'supplier'(供应商)、'deptTree'(部门树)、'categoryTree'(分类树)
 * @param params 可选参数，用于某些需要参数的字典类型
 * @returns 数据字典选项
 */
export function getDataOptions(dictName: string, params?: any) {
  // 生成缓存键，包含参数信息
  const cacheKey = params ? `${dictName}_${JSON.stringify(params)}` : dictName;

  // 检查缓存
  if (dataOptionsCache.has(cacheKey)) {
    return dataOptionsCache.get(cacheKey)!;
  }

  // 创建响应式数据
  const options = ref<Array<{ label: string; value: any }>>([]);

  // 缓存响应式数据
  dataOptionsCache.set(cacheKey, options);

  // 检查是否已有请求在进行中
  if (!dataRequestCache.has(cacheKey)) {
    let requestPromise: Promise<any>;

    if (dictName === 'brand') {
      requestPromise = getBrandDic();
    } else if (dictName === 'supplier') {
      requestPromise = getSupplierDic();
    } else if (dictName === 'deptTree') {
      requestPromise = getDeptTree();
    } else if (dictName === 'categoryTree') {
      requestPromise = categoryTree(params);
    } else {
      console.warn(`未知的字典类型: ${dictName}`);
      return options;
    }

    // 缓存请求Promise
    const cachedRequest = requestPromise
      .then((resp: any) => {
        console.log(`${dictName}字典响应:`, resp);

        if (dictName === 'deptTree') {
          // 部门树数据结构处理：保持原始树形结构
          options.value = resp;
        } else if (dictName === 'categoryTree') {
          // 分类树数据结构处理：保持原始树形结构，数据已经是正确的格式
          // 接口返回的数据格式：{ id, label, children?, parentId, weight, disabled }
          options.value = resp;
        } else {
          // 品牌和供应商字典数据结构处理
          options.value = resp.map((item: any) => ({
            label: item.label,
            value: item.value,
          }));
        }
      })
      .catch((error) => {
        console.error(`获取${dictName}字典失败:`, error);
        // 请求失败时清除缓存，允许重试
        dataOptionsCache.delete(cacheKey);
      })
      .finally(() => {
        // 请求完成后清除请求缓存
        dataRequestCache.delete(cacheKey);
      });

    dataRequestCache.set(cacheKey, cachedRequest);
  }

  return options;
}

/**
 * 清除数据选项缓存
 * @param dictName 字典名称，不传则清除所有缓存
 * @param params 可选参数，用于清除特定参数的缓存
 */
export function clearDataOptionsCache(dictName?: string, params?: any) {
  if (dictName) {
    const cacheKey = params ? `${dictName}_${JSON.stringify(params)}` : dictName;

    if (params) {
      // 清除特定参数的缓存
      dataOptionsCache.delete(cacheKey);
      dataRequestCache.delete(cacheKey);
      console.log(`已清除 ${dictName} (${JSON.stringify(params)}) 字典缓存`);
    } else {
      // 清除所有相关的缓存（包括带参数的）
      const keysToDelete: string[] = [];
      for (const key of dataOptionsCache.keys()) {
        if (key === dictName || key.startsWith(`${dictName}_`)) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => {
        dataOptionsCache.delete(key);
        dataRequestCache.delete(key);
      });
      console.log(`已清除 ${dictName} 相关的所有字典缓存`);
    }
  } else {
    dataOptionsCache.clear();
    dataRequestCache.clear();
    console.log('已清除所有数据字典缓存');
  }
}

/**
 * 从品牌字典中根据value查找对应的label
 * @param brandValue 品牌值
 * @returns 品牌名称，如果未找到则返回原值
 */
export function getBrandLabel(brandValue: string | number): string {
  if (!brandValue) return '';

  const brandOptions = getDataOptions('brand');
  const options = brandOptions.value;

  if (!options || !Array.isArray(options)) return String(brandValue);

  const brandOption = options.find(option =>
    String(option.value) === String(brandValue)
  );

  return brandOption ? brandOption.label : String(brandValue);
}

/**
 * 从供应商字典中根据value查找对应的label
 * @param supplierValue 供应商值
 * @returns 供应商名称，如果未找到则返回原值
 */
export function getSupplierLabel(supplierValue: string | number): string {
  if (!supplierValue) return '';

  const supplierOptions = getDataOptions('supplier');
  const options = supplierOptions.value;

  if (!options || !Array.isArray(options)) return String(supplierValue);

  const supplierOption = options.find(option =>
    String(option.value) === String(supplierValue)
  );

  return supplierOption ? supplierOption.label : String(supplierValue);
}
