import type {
  AssetFinanceForm,
  AssetFinanceQuery,
  AssetFinanceVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询资产财务信息列表
 * @param params
 * @returns 资产财务信息列表
 */
export function assetFinanceList(params?: AssetFinanceQuery) {
  return requestClient.get<PageResult<AssetFinanceVO>>(
    '/asset/assetFinance/list',
    { params },
  );
}

/**
 * 导出资产财务信息列表
 * @param params
 * @returns 资产财务信息列表
 */
export function assetFinanceExport(params?: AssetFinanceQuery) {
  return commonExport('/asset/assetFinance/export', params ?? {});
}

/**
 * 查询资产财务信息详情
 * @param financeId id
 * @returns 资产财务信息详情
 */
export function assetFinanceInfo(financeId: ID) {
  return requestClient.get<AssetFinanceVO>(`/asset/assetFinance/${financeId}`);
}

/**
 * 新增资产财务信息
 * @param data
 * @returns void
 */
export function assetFinanceAdd(data: AssetFinanceForm) {
  return requestClient.postWithMsg<void>('/asset/assetFinance', data);
}

/**
 * 更新资产财务信息
 * @param data
 * @returns void
 */
export function assetFinanceUpdate(data: AssetFinanceForm) {
  return requestClient.putWithMsg<void>('/asset/assetFinance', data);
}

/**
 * 删除资产财务信息
 * @param financeId id
 * @returns void
 */
export function assetFinanceRemove(financeId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/asset/assetFinance/${financeId}`);
}
