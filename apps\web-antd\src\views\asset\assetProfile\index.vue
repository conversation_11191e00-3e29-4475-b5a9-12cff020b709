<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { assetProfileExport, assetProfileList } from '#/api/asset/assetProfile';
import { commonDownloadExcel } from '#/utils/file/download';

import assetProfileFinanceModal from './assetProfile-finance-modal.vue';
import assetProfileHistoryModal from './assetProfile-history-modal.vue';
import assetProfileModal from './assetProfile-modal.vue';
import { columns, querySchema } from './data';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 折叠配置
  collapsed: true, // 默认折叠
  collapsedRows: 4, // 折叠时显示4行（显示到资产类别字段）
  showCollapseButton: true, // 显示折叠/展开按钮
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await assetProfileList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'managementId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'asset-assetProfile-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [AssetProfileModal] = useVbenModal({
  connectedComponent: assetProfileModal,
});

/**
 * 导入历史台账
 */
const [AssetProfileHistoryModal, importModalApi] = useVbenModal({
  connectedComponent: assetProfileHistoryModal,
});

/**
 * 财务入账
 */
const [AssetProfileFinanceModal, financeModalApi] = useVbenModal({
  connectedComponent: assetProfileFinanceModal,
});

const router = useRouter();

function handleAdd() {
  // 跳转到资产登记页面
  router.push('/asset/assetRegister');
}

async function handleEdit(row: any) {
  // 跳转到资产编辑页面，传递资产ID作为路由参数
  router.push({
    path: '/asset/assetEdit',
    query: {
      id: row.assetId,
      financeId: row.financeId,
      managementId: row.managementId,
    },
  });
}

// async function handleDelete(row: Required<AssetProfileForm>) {
//   await assetProfileRemove(row.assetId);
//   await tableApi.query();
// }

// function handleMultiDelete() {
//   const rows = tableApi.grid.getCheckboxRecords();
//   const ids = rows.map((row: Required<AssetProfileForm>) => row.assetId);
//   Modal.confirm({
//     title: '提示',
//     okType: 'danger',
//     content: `确认删除选中的${ids.length}条记录吗？`,
//     onOk: async () => {
//       await assetProfileRemove(ids);
//       await tableApi.query();
//     },
//   });
// }

function handleDownloadExcel() {
  commonDownloadExcel(
    assetProfileExport,
    '资产数据',
    tableApi.formApi.form.values,
    {
      fieldMappingTime: formOptions.fieldMappingTime,
    },
  );
}

/**
 * 处理导入历史台账
 */
function handleImportHistory() {
  importModalApi.open();
}

/**
 * 处理财务入账
 */
function handleFinanceImport() {
  financeModalApi.open();
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="资产列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['asset:assetProfile:import']"
            @click="handleImportHistory"
          >
            导入历史台账
          </a-button>
          <a-button
            v-access:code="['asset:assetProfile:import']"
            @click="handleFinanceImport"
          >
            财务入账
          </a-button>
          <a-button
            v-access:code="['asset:assetProfile:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <!-- <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['asset:assetProfile:remove']"
            @click="handleMultiDelete">
            {{ $t('pages.common.delete') }}
          </a-button> -->
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['asset:assetProfile:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <!-- <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['asset:assetProfile:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm> -->
        </Space>
      </template>
    </BasicTable>
    <AssetProfileModal @reload="tableApi.query()" />
    <AssetProfileHistoryModal @reload="tableApi.query()" />
    <AssetProfileFinanceModal @reload="tableApi.query()" />
  </Page>
</template>
