import type { BaseEntity, PageQuery } from '#/api/common';

export interface InventoryTaskVO {
  /**
   * 任务ID
   */
  taskId: bigint | string;

  /**
   * 任务名称
   */
  taskName: string;

  /**
   * 盘点范围
   */
  inventoryScope: number;

  /**
   * 盘点部门
   */
  inventoryDept: string | string[] | undefined;

  /**
   * 计划开始时间
   */
  taskStartTime: string;

  /**
   * 计划结束时间
   */
  taskEndTime: string;

  /**
   * 是否定时盘点
   */
  isScheduled: string;

  /**
   * 盘点人
   */
  inventoryPerson: string;

  /**
   * 盘点状态
   */
  inventoryStatus: number;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 备注
   */
  remark: string;
}

export interface InventoryTaskForm extends BaseEntity {
  /**
   * 任务ID
   */
  taskId?: bigint | string;

  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 盘点范围
   */
  inventoryScope?: number;

  /**
   * 盘点部门
   */
  inventoryDept?: string | string[];

  /**
   * 计划开始时间
   */
  taskStartTime?: string;

  /**
   * 计划结束时间
   */
  taskEndTime?: string;

  /**
   * 是否定时盘点
   */
  isScheduled?: string;

  /**
   * 盘点人
   */
  inventoryPerson?: string;
  /**
   * 盘点状态
   */
  inventoryStatus: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface InventoryTaskQuery extends PageQuery {
  /**
   * 任务名称
   */
  taskName?: string;

  /**
   * 盘点范围
   */
  inventoryScope?: number;

  /**
   * 盘点状态
   */
  inventoryStatus?: number;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
