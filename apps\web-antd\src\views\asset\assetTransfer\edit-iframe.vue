<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { cloneDeep } from '@vben/utils';

import { Card, message, Spin } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenModal } from '@vben/common-ui';
import { assetTransferAdd, assetTransferInfo, assetTransferUpdate } from '#/api/asset/assetTransfer';
import type { AssetProfileVO } from '#/api/asset/assetProfile/model';

import AssetTransferApplyModalComponent from './asset-transfer-apply-modal.vue';
import TransferDescription from './transfer-description.vue';
import { modalSchema } from './data';
import type { StartWorkFlowReqData } from '#/api/workflow/task/model';
import { startWorkFlow } from '#/api/workflow/task';
import type { AssetTransferVO } from '#/api/asset/assetTransfer/model';

const route = useRoute();

// 获取路由参数
const transferId = ref(route.query.id as string);
const readonly = route.query?.readonly === 'true';
const isLoading = ref(false);

// 计算属性
const isUpdate = computed(() => !!transferId.value);
const showActionBtn = computed(() => !readonly);

// 显示描述信息的状态
const showDescription = ref(false);
const transferDescription = ref<AssetTransferVO | null>(null);

// 显示详情时需要较小的padding
const cardSize = computed(() => {
  return showDescription.value ? 'small' : 'default';
});

// 选中的资产
const selectedAsset = ref<AssetProfileVO | null>(null);

const [AssetTransferForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 120,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
      disabled: readonly, // 只读模式下禁用所有表单项
    }
  },
  schema: modalSchema(!readonly), // 传递编辑模式参数
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
});

// 资产调拨流程申请模态框
const [AssetTransferApplyModal, assetTransferApplyModalApi] = useVbenModal({
  connectedComponent: AssetTransferApplyModalComponent,
});

// 加载数据
async function loadData() {
  if (!isUpdate.value || !transferId.value) {
    return;
  }

  try {
    isLoading.value = true;
    const record = await assetTransferInfo(transferId.value);

    // 检查返回的数据是否有效
    if (!record) {
      message.error('未找到对应的资产调拨记录');
      return;
    }

    // 如果是只读模式，显示描述信息
    if (readonly) {
      showDescription.value = true;
      transferDescription.value = record;
    } else {
      await formApi.setValues(record);
    }

    // 如果有资产信息，设置选中的资产
    if (record && record.assetId && record.assetCode) {
      selectedAsset.value = {
        assetId: record.assetId,
        assetCode: record.assetCode,
        assetName: record.assetName,
        brand: record.brand,
        model: record.model,
        serialNumber: record.serialNumber,
        manageDept: record.manageDept,
        useDept: record.deptOutId,
      } as unknown as AssetProfileVO;
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败');
  } finally {
    isLoading.value = false;
  }
}

// 保存数据
async function handleSave() {
  try {
    isLoading.value = true;
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请检查表单填写是否正确');
      return;
    }

    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());

    let result;
    if (isUpdate.value) {
      await assetTransferUpdate(data);
      message.success('更新成功');
      result = data; // 更新时返回原数据
    } else {
      result = await assetTransferAdd(data);
      message.success('新增成功');
    }

    return result;
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
    throw error;
  } finally {
    isLoading.value = false;
  }
}

// 专门为启动流程使用的保存函数（不跳转页面）
async function handleSaveForWorkflow() {
  try {
    isLoading.value = true;
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请检查表单填写是否正确');
      return;
    }

    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());

    let result;
    if (isUpdate.value) {
      await assetTransferUpdate(data);
      message.success('更新成功');
      result = data; // 更新时返回原数据
    } else {
      result = await assetTransferAdd(data);
      message.success('新增成功');
    }

    return result;
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
    throw error;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 保存业务 & 发起流程
 */
async function handleStartWorkFlow() {
  try {
    // 保存业务（不跳转）
    const resp = await handleSaveForWorkflow();
    console.log('保存业务成功:', resp);
    // 启动流程
    const taskVariables = {
    };
    const flowCode = 'asset_transfer';
    const startWorkFlowData: StartWorkFlowReqData = {
      businessId: resp!.transferId,
      flowCode,
      variables: taskVariables,
    };
    const { taskId } = await startWorkFlow(startWorkFlowData);
    console.log('启动流程成功:', taskId);
    
    // 打开资产调拨流程申请弹窗
    assetTransferApplyModalApi.setData({
      taskId,
      taskVariables,
      variables: {
        deptOutId: resp!.deptOutId,
        deptInId: resp!.deptInId,
      },
    });
    assetTransferApplyModalApi.open();
  } catch (error) {
    console.error(error);
  }
}

// 流程完成处理
function handleComplete() {
  formApi.resetForm();
  // 在iframe中不需要跳转
}

// iframe 通信函数
function postMessageToParent(type: string, data?: any) {
  try {
    if (window.parent && window.parent !== window) {
      const message = { type, ...data };
      window.parent.postMessage(message, '*');
    }
  } catch (error) {
    console.error('发送消息到父窗口失败:', error);
  }
}

// 发送高度信息到父窗口
function updateIframeHeight() {
  try {
    const height = document.documentElement.scrollHeight || document.body.scrollHeight || 600;
    postMessageToParent('height', { height });
  } catch (error) {
    console.error('更新iframe高度失败:', error);
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  try {
    // 如果是编辑或查看模式，加载数据
    if (isUpdate.value || readonly) {
      await loadData();
    } else {
      // 新增模式，初始化表单数据
      formApi.setValues({
        applicationDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
      });
    }
  } catch (error) {
    console.error('组件初始化失败:', error);
  }

  // 通知父窗口加载完成
  setTimeout(() => {
    postMessageToParent('mounted');
    updateIframeHeight();
  }, 100);

  // 监听DOM变化，自动更新高度
  try {
    const observer = new MutationObserver(() => {
      updateIframeHeight();
    });

    if (document.body) {
      observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
      });
    }
  } catch (error) {
    console.error('设置MutationObserver失败:', error);
  }
});

</script>

<template>
  <Card id="asset-transfer-form" :size="cardSize">
    <!-- 加载状态 -->
    <div v-if="isLoading" style="text-align: center; padding: 20px;">
      <Spin size="large" tip="加载中..." />
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 使用v-show会影响生命周期 -->
      <AssetTransferForm v-show="!showDescription" />
      <TransferDescription v-if="showDescription && transferDescription" :data="transferDescription" />

      <!-- 如果是只读模式但没有数据 -->
      <div v-if="showDescription && !transferDescription" style="text-align: center; padding: 20px; color: #999;">
        暂无数据
      </div>

      <div v-if="showActionBtn" class="flex justify-end gap-2 mt-4">
        <a-button type="primary" :loading="isLoading" @click="handleSave">
          {{ isUpdate ? '更新' : '保存' }}
        </a-button>
        <a-button type="primary" :loading="isLoading" @click="handleStartWorkFlow">
          提交
        </a-button>
      </div>
    </div>

    <AssetTransferApplyModal @complete="handleComplete" />
  </Card>
</template>

<style lang="scss">
html:has(#asset-transfer-form) {
  /**
  去除顶部进度条样式
  */
  #nprogress {
    display: none;
  }
}

/* iframe 模式下的样式调整 */
#asset-transfer-form {
  /* 确保在iframe中有合适的内边距 */
  padding: 0;
  margin: 0;
  
  /* 确保内容不会被截断 */
  min-height: 100vh;
  box-sizing: border-box;
  
  /* 处理可能的滚动问题 */
  overflow-y: auto;
  
  .ant-card-body {
    /* 调整卡片内容的内边距 */
    padding: 16px;
  }
  
  .ant-form {
    /* 确保表单在iframe中正常显示 */
    max-width: 100%;
  }
  
  /* 移除卡片的边框和阴影，在iframe中可能显示异常 */
  border: none;
  box-shadow: none;
}
</style>
