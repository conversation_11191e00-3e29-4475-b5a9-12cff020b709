<script setup lang="ts">
import type { SupplierForm } from '#/api/asset/supplier/model';

import { nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { Button, Card, Col, message, Row, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  getContactList,
  getQualificationList,
  supplierInfo,
  supplierUpdate,
} from '#/api/asset/supplier';

import {
  basicInfoSchema,
  createContactSchema,
  createQualificationSchema,
} from './edit-data';

const emit = defineEmits<{ reload: [] }>();

const route = useRoute();
const router = useRouter();

// 获取供应商ID
const supplierId = ref(route.query.id as string);
const isLoading = ref(false);

// 联系人数量
const contactCount = ref(1);

// 企业资质数量
const qualificationCount = ref(1);

// 基本信息表单
const [BasicInfoForm, basicInfoApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: basicInfoSchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
  showDefaultActions: false,
});

// 联系人表单 - 初始化为空schema，后续动态添加
const [ContactForm, contactApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: [], // 初始为空，后续动态添加
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
  showDefaultActions: false,
});

// 企业资质表单 - 初始化为空schema，后续动态添加
const [QualificationForm, qualificationApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: [], // 初始为空，后续动态添加
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
  showDefaultActions: false,
});

// 加载供应商数据
async function loadSupplierData() {
  if (!supplierId.value) {
    message.error('缺少供应商ID参数');
    router.push('/supplier/list');
    return;
  }

  try {
    // isLoading.value = true;
    console.log('开始加载供应商数据，ID:', supplierId.value);
    const data = await supplierInfo(supplierId.value);
    if (data.companyType && typeof data.companyType === 'string') {
      const originalValue = data.companyType;
      (data as any).companyType = data.companyType
        .split(',')
        .map((type: string) => type.trim())
        .filter(Boolean);
      console.log('companyType转换:', originalValue, '->', data.companyType);
    }
    await basicInfoApi.setValues(data);

    // 获取并设置联系人信息
    try {
      const contactList = await getContactList(supplierId.value);
      contactCount.value = contactList.length || 1; // 至少保证有一个联系人
      // 初始化联系人表单schema
      initContactSchema();
      // 设置联系人数据
      const contactData = { contacts: contactList.length > 0 ? contactList : [{}] };
      await contactApi.setValues(contactData);
    } catch (error) {
      console.error('获取联系人列表失败:', error);
      // 如果获取联系人失败，至少保证有一个空的联系人表单
      contactCount.value = 1;
      initContactSchema();
    }

    // 获取并设置企业资质信息
    try {
      const qualificationList = await getQualificationList(supplierId.value);
      qualificationCount.value = qualificationList.length || 1; // 至少保证有一个企业资质
      // 初始化企业资质表单schema
      initQualificationSchema();
      // 设置企业资质数据
      const qualificationData = { qualifications: qualificationList.length > 0 ? qualificationList : [{}] };
      await qualificationApi.setValues(qualificationData);
    } catch (error) {
      console.error('获取企业资质列表失败:', error);
      // 如果获取企业资质失败，至少保证有一个空的企业资质表单
      qualificationCount.value = 1;
      initQualificationSchema();
    }
  } catch (error) {
    console.error('加载供应商数据失败:', error);
    message.error('加载供应商数据失败');
  } finally {
    console.log('数据加载完成，关闭loading状态');
    isLoading.value = false;
  }
}

// 初始化联系人表单schema
function initContactSchema() {
  const schemas: any[] = [];
  for (let i = 0; i < contactCount.value; i++) {
    schemas.push(...createContactSchema(i));
  }

  contactApi.setState(() => {
    return {
      schema: schemas,
    };
  });
}

// 初始化企业资质表单schema
function initQualificationSchema() {
  const schemas: any[] = [];
  for (let i = 0; i < qualificationCount.value; i++) {
    schemas.push(...createQualificationSchema(i));
  }

  qualificationApi.setState(() => {
    return {
      schema: schemas,
    };
  });
}

// 新增联系人 - 参考添加实物清单项的实现
function addContact() {
  const currentIndex = contactCount.value;
  const addSchema = createContactSchema(currentIndex);

  contactApi.setState((prev) => {
    const currentSchema = prev?.schema ?? [];
    return {
      schema: [...currentSchema, ...addSchema],
    };
  });

  // 增加计数器
  contactCount.value++;
  console.log('新增联系人成功，当前联系人数量:', contactCount.value);
}

// 删除联系人 - 参考删除实物清单项的实现
function removeContact() {
  if (contactCount.value > 1) {
    contactApi.setState((prev) => {
      const currentSchema = prev?.schema ?? [];
      // 每个联系人包含6个字段
      const fieldsPerContact = 6;
      const newSchema = currentSchema.slice(0, -fieldsPerContact);

      return {
        schema: newSchema,
      };
    });

    contactCount.value--;
    console.log('删除联系人成功，当前联系人数量:', contactCount.value);
  } else {
    message.warning('至少需要保留一个联系人');
  }
}

// 新增企业资质
function addQualification() {
  const currentIndex = qualificationCount.value;
  const addSchema = createQualificationSchema(currentIndex);

  qualificationApi.setState((prev) => {
    const currentSchema = prev?.schema ?? [];
    return {
      schema: [...currentSchema, ...addSchema],
    };
  });

  // 增加计数器
  qualificationCount.value++;
  console.log('新增企业资质成功，当前企业资质数量:', qualificationCount.value);
}

// 删除企业资质
function removeQualification() {
  if (qualificationCount.value > 1) {
    qualificationApi.setState((prev) => {
      const currentSchema = prev?.schema ?? [];
      // 每个企业资质包含5个字段
      const fieldsPerQualification = 5;
      const newSchema = currentSchema.slice(0, -fieldsPerQualification);

      return {
        schema: newSchema,
      };
    });

    qualificationCount.value--;
    console.log('删除企业资质成功，当前企业资质数量:', qualificationCount.value);
  } else {
    message.warning('至少需要保留一个企业资质');
  }
}

// 保存数据
async function handleSave() {
  try {
    // isLoading.value = true;

    // 验证表单
    const [basicValid, contactValid, qualificationValid] = await Promise.all([
      basicInfoApi.validate(),
      contactApi.validate(),
      qualificationApi.validate(),
    ]);

    if (!basicValid.valid || !contactValid.valid || !qualificationValid.valid) {
      message.error('请检查表单填写是否正确');
      return;
    }

    // 获取表单数据
    const basicData = cloneDeep(await basicInfoApi.getValues());
    const contactData = cloneDeep(await contactApi.getValues());
    const qualificationData = cloneDeep(await qualificationApi.getValues());

    // 处理companyType字段：将数组转换为字符串
    if (basicData.companyType && Array.isArray(basicData.companyType)) {
      basicData.companyType = basicData.companyType.join(',');
    }

    // 合并数据
    const allFormData: any = {
      supplier: basicData,
      contacts: contactData.contacts || [],
      qualifications: qualificationData.qualifications || [],
    };

    console.log('供应商数据:', allFormData);

    // 调用更新接口
    await supplierUpdate(allFormData);
    message.success('保存成功');
    emit('reload');
    router.push('/supplier/list');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    isLoading.value = false;
  }
}

// 返回列表
function handleBack() {
  router.push('/asset/supplier');
}

// 组件挂载时加载数据
onMounted(() => {
  initContactSchema();
  initQualificationSchema();
  loadSupplierData();
});
</script>

<template>
  <Page>
    <Row :gutter="[15, 15]">
      <!-- 基本信息卡片 -->
      <Col :span="24">
      <Card>
        <template #title>
          <span style="font-size: 20px">基本信息</span>
        </template>
        <template #extra>
          <Button type="primary" :loading="isLoading" @click="handleSave">
            保存
          </Button>
          <Button @click="handleBack"> 返回 </Button>
        </template>
        <BasicInfoForm />
      </Card>
      </Col>

      <!-- 联系人信息卡片 -->
      <Col :span="24">
      <Card :loading="isLoading">
        <template #title>
          <Space>
            <span>联系人信息</span>
            <Button type="primary" size="small" @click="addContact">
              新增联系人
            </Button>
            <Button v-if="contactCount > 1" danger size="small" @click="removeContact">
              删除联系人
            </Button>
          </Space>
        </template>
        <ContactForm />
      </Card>
      </Col>

      <!-- 企业资质信息卡片 -->
      <Col :span="24">
      <Card :loading="isLoading">
        <template #title>
          <Space>
            <span>企业资质信息</span>
            <Button type="primary" size="small" @click="addQualification">
              新增资质
            </Button>
            <Button v-if="qualificationCount > 1" danger size="small" @click="removeQualification">
              删除资质
            </Button>
          </Space>
        </template>
        <QualificationForm />
      </Card>
      </Col>
    </Row>
  </Page>
</template>
