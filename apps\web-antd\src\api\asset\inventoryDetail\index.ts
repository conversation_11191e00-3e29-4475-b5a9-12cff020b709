import type {
  InventoryDetailForm,
  InventoryDetailQuery,
  InventoryDetailVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询资产盘点详情列表
 * @param params
 * @returns 资产盘点详情列表
 */
export function inventoryDetailList(params?: InventoryDetailQuery) {
  return requestClient.get<PageResult<InventoryDetailVO>>(
    '/asset/inventoryDetail/list',
    { params },
  );
}

/**
 * 导出资产盘点详情列表
 * @param params
 * @returns 资产盘点详情列表
 */
export function inventoryDetailExport(params?: InventoryDetailQuery) {
  return commonExport('/asset/inventoryDetail/export', params ?? {});
}

/**
 * 查询资产盘点详情详情
 * @param detailId id
 * @returns 资产盘点详情详情
 */
export function inventoryDetailInfo(detailId: ID) {
  return requestClient.get<InventoryDetailVO>(
    `/asset/inventoryDetail/${detailId}`,
  );
}

/**
 * 新增资产盘点详情
 * @param data
 * @returns void
 */
export function inventoryDetailAdd(data: InventoryDetailForm) {
  return requestClient.postWithMsg<void>('/asset/inventoryDetail', data);
}

/**
 * 更新资产盘点详情
 * @param data
 * @returns void
 */
export function inventoryDetailUpdate(data: InventoryDetailForm) {
  return requestClient.putWithMsg<void>('/asset/inventoryDetail', data);
}

/**
 * 删除资产盘点详情
 * @param detailId id
 * @returns void
 */
export function inventoryDetailRemove(detailId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(
    `/asset/inventoryDetail/${detailId}`,
  );
}

/**
 * 更新资产盘点任务
 * @param data
 * @returns void
 */
export function inventoryDetailUpdateStatus(data: InventoryDetailForm) {
  return requestClient.putWithMsg<void>(
    '/asset/inventoryDetail/changeStatus',
    data,
  );
}
