<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { addFullName, cloneDeep, getPopupContainer } from '@vben/utils';

import { <PERSON><PERSON>, Card, Col, message, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { assetProfileAdd } from '#/api/asset/assetProfile';
import { categoryTree } from '#/api/asset/category/type';
import { getDeptTree } from '#/api/asset/assetProfile';

import { baseSchema, entitySchema, financeSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const [BaseForm, baseApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: baseSchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});

const [FinanceForm, financeApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: financeSchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});

const [EntityForm, entityformApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: entitySchema(),
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
  showDefaultActions: false,
});

async function onSubmit(status: string) {
  baseApi.setFieldValue('status',status);
  // 验证所有表单
  const [baseValid, financeValid, entityValid] = await Promise.all([
    baseApi.validate(),
    financeApi.validate(),
    entityformApi.validate(),
  ]);

  if (!baseValid.valid || !financeValid.valid || !entityValid.valid) {
    message.error('请检查表单填写是否正确');
    return;
  }

  try {
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const baseData = cloneDeep(await baseApi.getValues());
    const financeData = cloneDeep(await financeApi.getValues());
    const entityData = cloneDeep(await entityformApi.getValues());

    console.log('基本信息JSON数据:', baseData);
    console.log('财务信息JSON数据:', financeData);
    console.log('实物清单JSON数据:', entityData);

    // 合并三个表单的JSON数据
    const allFormData: any = {
      profile: baseData,
      finance: financeData,
      entityList: entityData.entityList,
    };

    console.log('合并后的JSON数据:', allFormData);

    // 提交数据
    await assetProfileAdd(allFormData);

    message.success('资产登记成功');
    emit('reload');
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败，请重试');
  }
}

// 实物清单的数量，至少保持一个
const entityListCount = ref(1);

// 部门树数据
const deptTree = ref<any[]>([]);

// 医疗器械分类树数据
const medicalDeviceCategoryTree = ref<any[]>([]);

// 安装位置树数据
const installLocationTree = ref<any[]>([]);
const fixedAssetCodeTree = ref<any[]>([]);

// 删除最后一个实物清单项（至少保留一个）
function deleteLastEntityItem() {
  if (entityListCount.value > 1) {
    entityformApi.setState((prev) => {
      const currentSchema = prev?.schema ?? [];
      // 每个实物清单项包含8个字段
      const fieldsPerItem = 8;
      const newSchema = currentSchema.slice(0, -fieldsPerItem);

      return {
        schema: newSchema,
      };
    });

    entityListCount.value--;
  }
}

// 创建单个实物清单项的表单配置
function createEntitySchemaItem(index: number) {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入标签二维码',
      },
      fieldName: `entityList[${index}].qrCode`,
      label: `标签二维码 ${index + 1}`,
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入序列号',
      },
      fieldName: `entityList[${index}].serialNumber`,
      label: `序列号 ${index + 1}`,
    },
    {
      component: 'TreeSelect',
      componentProps: () => ({
        class: 'w-full',
        fieldNames: {
          key: 'id',
          value: 'id',
          children: 'children',
        },
        getPopupContainer,
        placeholder: '请选择使用科室',
        showSearch: true,
        treeData: deptTree.value,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        // 筛选的字段
        treeNodeFilterProp: 'label',
        // 选中后显示在输入框的值
        treeNodeLabelProp: 'fullName',
      }),
      fieldName: `entityList[${index}].deptSection`,
      label: `使用科室 ${index + 1}`,
    },
    {
      component: 'TreeSelect',
      componentProps: () => ({
        class: 'w-full',
        fieldNames: {
          key: 'id',
          value: 'id',
          children: 'children',
          label: 'label',
        },
        getPopupContainer,
        placeholder: '请选择安装位置',
        showSearch: true,
        treeData: installLocationTree.value,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        // 筛选的字段
        treeNodeFilterProp: 'label',
        // 选中后显示在输入框的值
        treeNodeLabelProp: 'fullName',
        // 确保回显正确显示
        displayRender: (labels: string[], selectedOptions: any[]) => {
          if (selectedOptions && selectedOptions.length > 0) {
            const lastOption = selectedOptions[selectedOptions.length - 1];
            return lastOption?.fullName || labels.join(' / ');
          }
          return labels.join(' / ');
        },
      }),
      fieldName: `entityList[${index}].installationLocation`,
      label: `安装位置 ${index + 1}`,
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请输入生产日期',
      },
      fieldName: `entityList[${index}].productionDate`,
      label: `生产日期 ${index + 1}`,
    },
    {
      component: 'RangePicker',
      componentProps: {
        placeholder: '请输入保修期限',
      },
      defaultValue: undefined,
      fieldName: `entityList[${index}].warrantyDate`,
      label: `保险期限 ${index + 1}`,
      rules: 'selectRequired',
    },
    {
      label: `图片上传 ${index + 1}`,
      component: 'ImageUpload',
      fieldName: `entityList[${index}].itemPhotoList`,
      componentProps: {
        maxCount: 5,
      },
    },
    {
      component: 'Br',
      fieldName: '',
      label: '',
    },
  ];
}

function updateSchema() {
  const currentIndex = entityListCount.value;
  const addSchema = createEntitySchemaItem(currentIndex);

  entityformApi.setState((prev) => {
    const currentSchema = prev?.schema ?? [];
    return {
      schema: [...currentSchema, ...addSchema],
    };
  });

  // 增加计数器
  entityListCount.value++;
}

// 重置实物清单（保留一个）
function resetEntityList() {
  entityformApi.setState(() => {
    return {
      schema: createEntitySchemaItem(0),
    };
  });
  entityListCount.value = 1;
}

/**
 * 初始化部门选择
 */
async function setupDeptSelect() {
  try {
    // 获取部门树数据
    const deptTreeData = await getDeptTree();
    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(deptTreeData, 'label', ' / ');
    deptTree.value = deptTreeData;

    // 更新实体表单中的部门选择字段
    entityformApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
          },
          getPopupContainer,
          placeholder: '请选择使用科室',
          showSearch: true,
          treeData: deptTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
        }),
        fieldName: 'entityList[0].deptSection',
        label: '使用科室',
      },
    ]);
  } catch (error) {
    console.error('初始化部门选择失败:', error);
  }
}

/**
 * 初始化医疗器械分类选择
 */
async function setupMedicalDeviceCategorySelect() {
  try {
    // 获取医疗器械分类树数据
    const categoryData = await categoryTree('ast_medical_device_category');
    console.log('医疗器械分类树数据:', categoryData);

    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(categoryData, 'label', ' / ');
    medicalDeviceCategoryTree.value = categoryData;

    // 更新基础表单中的医疗器械分类字段
    baseApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择医疗器械分类',
          showSearch: true,
          treeData: medicalDeviceCategoryTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
          // 确保回显正确显示
          displayRender: (labels: string[], selectedOptions: any[]) => {
            if (selectedOptions && selectedOptions.length > 0) {
              const lastOption = selectedOptions[selectedOptions.length - 1];
              return lastOption?.fullName || labels.join(' / ');
            }
            return labels.join(' / ');
          },
        }),
        fieldName: 'medicalDeviceCategory',
        label: '医疗器械分类',
      },
    ]);
  } catch (error) {
    console.error('初始化医疗器械分类选择失败:', error);
  }
}

/**
 * 初始化安装位置选择
 */
async function setupInstallLocationSelect() {
  try {
    // 获取安装位置树数据
    const locationData = await categoryTree('ast_location');
    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(locationData, 'label', ' / ');
    installLocationTree.value = locationData;

    // 更新基础表单中的安装位置字段
    entityformApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => {
          return {
            class: 'w-full',
            fieldNames: {
              key: 'id',
              value: 'id',
              children: 'children',
              label: 'label',
            },
            getPopupContainer,
            placeholder: '请选择安装位置',
            showSearch: true,
            treeData: installLocationTree.value,
            treeDefaultExpandAll: true,
            treeLine: { showLeafIcon: false },
            // 筛选的字段
            treeNodeFilterProp: 'label',
            // 选中后显示在输入框的值
            treeNodeLabelProp: 'fullName',
            // 确保回显正确显示
            displayRender: (labels: string[], selectedOptions: any[]) => {
              if (selectedOptions && selectedOptions.length > 0) {
                const lastOption = selectedOptions[selectedOptions.length - 1];
                return lastOption?.fullName || labels.join(' / ');
              }
              return labels.join(' / ');
            },
          };
        },
        fieldName: 'entityList[0].installationLocation',
        label: '安装位置',
      },
    ]);
  } catch (error) {
    console.error('初始化安装位置选择失败:', error);
  }
}
/**
 * 初始化医疗器械分类选择
 */
async function setupFixedAssetCodeSelect() {
  try {
    // 获取医疗器械分类树数据
    const categoryData = await categoryTree('ast_state_asset_category');
    console.log('固定资产分类树数据:', categoryData);

    // 选中后显示在输入框的值 即父节点 / 子节点
    addFullName(categoryData, 'label', ' / ');
    fixedAssetCodeTree.value = categoryData;

    // 更新基础表单中的固定资产分类字段
    baseApi.updateSchema([
      {
        component: 'TreeSelect',
        componentProps: () => ({
          class: 'w-full',
          fieldNames: {
            key: 'id',
            value: 'id',
            children: 'children',
            label: 'label',
          },
          getPopupContainer,
          placeholder: '请选择固定资产分类',
          showSearch: true,
          treeData: fixedAssetCodeTree.value,
          treeDefaultExpandAll: true,
          treeLine: { showLeafIcon: false },
          // 筛选的字段
          treeNodeFilterProp: 'label',
          // 选中后显示在输入框的值
          treeNodeLabelProp: 'fullName',
          // 确保回显正确显示
          displayRender: (labels: string[], selectedOptions: any[]) => {
            if (selectedOptions && selectedOptions.length > 0) {
              const lastOption = selectedOptions[selectedOptions.length - 1];
              return lastOption?.fullName || labels.join(' / ');
            }
            return labels.join(' / ');
          },
        }),
        fieldName: 'fixedAssetCode',
        label: '固定资产分类',
      },
    ]);
  } catch (error) {
    console.error('初始化固定资产分类选择失败:', error);
  }
}

// 组件挂载时初始化各种下拉选择
onMounted(() => {
  setupDeptSelect();
  setupMedicalDeviceCategorySelect();
  setupInstallLocationSelect();
  setupFixedAssetCodeSelect();
});
</script>

<template>
  <Page>
    <Row :gutter="[15, 15]">
      <Col :span="24">
        <Card>
          <template #title>
            <span style="font-size: 20px">资产登记——录入新资产信息</span>
          </template>
          <template #extra>
            <Button class="ml-10">重置</Button>
            <Button class="ml-10" @click="onSubmit('0')">保存草稿</Button>
            <Button type="primary" class="ml-10" @click="onSubmit('1')">
              提交登记
            </Button>
          </template>
        </Card>
      </Col>

      <Col :span="24">
        <Card>
          <template #title>
            <span style="font-size: 20px">基本信息</span>
          </template>
          <el-divider />
          <BaseForm />
        </Card>
      </Col>
      <Col :span="24">
        <Card>
          <template #title>
            <span style="font-size: 20px">财务信息</span>
          </template>
          <el-divider />
          <FinanceForm />
        </Card>
      </Col>
      <Col :span="24">
        <Card>
          <template #title>
            <span style="font-size: 20px">实物清单</span>
          </template>
          <template #extra>
            <Button @click="updateSchema" type="primary">
              添加实物清单项
            </Button>
            <Button
              @click="deleteLastEntityItem"
              :disabled="entityListCount <= 1"
              class="ml-2"
            >
              删除最后一项
            </Button>
            <Button @click="resetEntityList" class="ml-2"> 重置</Button>
          </template>
          <el-divider />
          <EntityForm />
        </Card>
      </Col>
    </Row>
  </Page>
</template>
