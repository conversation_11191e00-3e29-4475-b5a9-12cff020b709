<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { TreeSelectProps } from 'ant-design-vue';
import type { RuleObject } from 'ant-design-vue/es/form';
import type { SelectValue } from 'ant-design-vue/es/select';

import type { InventoryTaskForm } from '#/api/asset/inventoryTask/model';

import { computed, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep, getPopupContainer } from '@vben/utils';

import {
  DatePicker,
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  TreeSelect,
} from 'ant-design-vue';
import { pick } from 'lodash-es';

import {
  inventoryTaskAdd,
  inventoryTaskInfo,
  inventoryTaskUpdate,
} from '#/api/asset/inventoryTask';
import { getDeptTree } from '#/api/system/user';
import { getDictOptions } from '#/utils/dict';
import { useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/**
 * 树形下拉框
 */
const SHOW_PARENT = TreeSelect.SHOW_PARENT;
const treeData = ref<TreeSelectProps['treeData']>([]);

onMounted(async () => {
  // 获取部门树数据
  const deptTree = await getDeptTree();
  // 选中后显示在输入框的值 即父节点 / 子节点
  addFullName(deptTree, 'label', ' / ');
  treeData.value = deptTree;
});
/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<InventoryTaskForm> = {
  taskId: undefined,
  taskName: undefined,
  inventoryScope: 0,
  inventoryDept: undefined,
  taskStartTime: undefined,
  taskEndTime: undefined,
  isScheduled: 'Y',
  inventoryPerson: undefined,
  remark: undefined,
};

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<InventoryTaskForm>>({
  taskName: [{ required: true, message: '任务名称不能为空' }],
  inventoryScope: [{ required: true, message: '盘点范围不能为空' }],
  taskStartTime: [{ required: true, message: '计划开始时间不能为空' }],
  taskEndTime: [{ required: true, message: '计划结束时间不能为空' }],
  isScheduled: [{ required: true, message: '是否定时盘点不能为空' }],
  inventoryPerson: [{ required: true, message: '盘点人不能为空' }],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[650px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await inventoryTaskInfo(id);
      if (typeof record.inventoryDept === 'string') {
        record.inventoryDept = record.inventoryDept
          ? record.inventoryDept.split(',')
          : undefined;
      }
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
      // 兼容部门选择(部门为空时候，pick会把undefined替换成null,导致框内多一个空白框)
      if (formData.value.inventoryDept === null) {
        formData.value.inventoryDept = undefined;
      }
      // 初始化inventoryDept是否必填
      handleInventoryScopeChange(formData.value.inventoryScope);
    }
    await markInitialized();

    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    // 添加数组转字符串逻辑
    if (Array.isArray(data.inventoryDept)) {
      data.inventoryDept = data.inventoryDept.join(',');
    }
    await (isUpdate.value ? inventoryTaskUpdate(data) : inventoryTaskAdd(data));
    resetInitialized();
    emit('reload');
    modalApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
  // 重置验证
  formRules.value.inventoryDept = [{ required: false }];
}

// 监听盘点范围变化
function handleInventoryScopeChange(value: SelectValue) {
  const numValue = typeof value === 'string' ? Number.parseInt(value) : value;
  if (numValue === 0) {
    formData.value.inventoryDept = undefined;
    formRules.value.inventoryDept = [{ required: false }];
  } else {
    formRules.value.inventoryDept = [
      { required: true, message: '盘点部门不能为空' },
    ];
  }
}
</script>

<template>
  <BasicModal :title="title">
    <Form :label-col="{ span: 4 }">
      <FormItem label="任务名称" v-bind="validateInfos.taskName">
        <Input
          v-model:value="formData.taskName"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="盘点范围" v-bind="validateInfos.inventoryScope">
        <Select
          v-model:value="formData.inventoryScope"
          :options="getDictOptions('ast_inventory_scope')"
          :get-popup-container="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
          @change="handleInventoryScopeChange"
        />
      </FormItem>
      <FormItem label="盘点部门" v-bind="validateInfos.inventoryDept">
        <TreeSelect
          v-model:value="formData.inventoryDept"
          :field-names="{ label: 'label', value: 'id' }"
          style="width: 100%"
          :tree-data="treeData"
          tree-checkable
          allow-clear
          :show-checked-strategy="SHOW_PARENT"
          placeholder="请选择"
          tree-node-filter-prop="label"
          tree-node-label-prop="fullName"
          :disabled="formData.inventoryScope === 0"
        />
      </FormItem>
      <FormItem label="开始时间" v-bind="validateInfos.taskStartTime">
        <!-- 需要自行调整参数 -->
        <DatePicker
          v-model:value="formData.taskStartTime"
          format="YYYY-MM-DD HH"
          value-format="YYYY-MM-DD HH:00:00"
          show-time
        />
      </FormItem>
      <FormItem label="结束时间" v-bind="validateInfos.taskEndTime">
        <!-- 需要自行调整参数 -->
        <DatePicker
          v-model:value="formData.taskEndTime"
          format="YYYY-MM-DD HH"
          value-format="YYYY-MM-DD HH:00:00"
          show-time
        />
      </FormItem>
      <FormItem label="定时盘点" v-bind="validateInfos.isScheduled">
        <Select
          v-model:value="formData.isScheduled"
          :options="getDictOptions('sys_yes_no')"
          :get-popup-container="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="盘点人" v-bind="validateInfos.inventoryPerson">
        <Input
          v-model:value="formData.inventoryPerson"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="备注" v-bind="validateInfos.remark">
        <Textarea
          v-model:value="formData.remark"
          :placeholder="$t('ui.formRules.required')"
          :rows="4"
        />
      </FormItem>
    </Form>
  </BasicModal>
</template>
