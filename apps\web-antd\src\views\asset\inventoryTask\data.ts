import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { deptDict } from '#/api/system/dept';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'taskName',
    label: '任务名称',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.AST_INVENTORY_TASK_STATUS 便于维护
      options: getDictOptions(DictEnum.AST_INVENTORY_TASK_STATUS),
    },
    fieldName: 'inventoryStatus',
    label: '盘点状态',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.AST_INVENTORY_SCOPE 便于维护
      options: getDictOptions(DictEnum.AST_INVENTORY_SCOPE, true),
    },
    fieldName: 'inventoryScope',
    label: '盘点范围',
  },
  {
    component: 'RangePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    fieldName: 'createTime',
    label: '创建时间',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '任务名称',
    field: 'taskName',
  },
  {
    title: '盘点范围',
    field: 'inventoryScope',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.AST_INVENTORY_SCOPE 便于维护
        return renderDict(row.inventoryScope, DictEnum.AST_INVENTORY_SCOPE);
      },
    },
  },
  {
    title: '盘点部门',
    field: 'inventoryDept',
    formatter: ({ cellValue }) => {
      if (!cellValue) return '';
      const ids = String(cellValue).split(',');
      const dict = deptDict.value;
      const names = ids.map((id) => {
        if (dict instanceof Map) {
          return dict.get(Number(id)) || id;
        } else if (dict && typeof dict === 'object') {
          return dict[id] || id;
        }
        return id;
      });
      return names.join('，');
    },
  },
  {
    title: '计划时间',
    field: 'taskStartTime',
    showOverflow: false,
    slots: {
      default: ({ row }) => {
        return `${row.taskStartTime}
        ${row.taskEndTime}`;
      },
    },
    width: 160,
  },
  {
    title: '是否定时盘点',
    field: 'isScheduled',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.SYS_YES_NO 便于维护
        return renderDict(row.isScheduled, DictEnum.SYS_YES_NO);
      },
    },
  },
  {
    title: '盘点人',
    field: 'inventoryPerson',
  },
  {
    title: '盘点状态',
    field: 'inventoryStatus',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.AST_INVENTORY_TASK_STATUS 便于维护
        return renderDict(
          row.inventoryStatus,
          DictEnum.AST_INVENTORY_TASK_STATUS,
        );
      },
    },
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 240,
  },
];
