<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';

import type { InventoryDetailForm } from '#/api/asset/inventoryDetail/model';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, getPopupContainer } from '@vben/utils';

import { Form, FormItem, Input, Select, Textarea } from 'ant-design-vue';
import { pick } from 'lodash-es';

import {
  inventoryDetailAdd,
  inventoryDetailInfo,
  inventoryDetailUpdate,
} from '#/api/asset/inventoryDetail';
import { getDictOptions } from '#/utils/dict';
import { useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<InventoryDetailForm> = {
  detailId: undefined,
  taskId: undefined,
  assetId: undefined,
  qrCode: undefined,
  assetCode: undefined,
  assetName: undefined,
  useDept: undefined,
  useLocal: undefined,
  inventoryStatus: undefined,
  remark: undefined,
};

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<InventoryDetailForm>>({
  qrCode: [{ required: true, message: '物联二维码不能为空' }],
  assetCode: [{ required: true, message: '固定资产编码不能为空' }],
  assetName: [{ required: true, message: '资产名称不能为空' }],
  useDept: [{ required: true, message: '使用科室不能为空' }],
  useLocal: [{ required: true, message: '使用地址不能为空' }],
  remark: [{ required: true, message: '备注不能为空' }],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await inventoryDetailInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }
    await markInitialized();

    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    await (isUpdate.value
      ? inventoryDetailUpdate(data)
      : inventoryDetailAdd(data));
    resetInitialized();
    emit('reload');
    modalApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
}
</script>

<template>
  <BasicModal :title="title">
    <Form :label-col="{ span: 4 }">
      <FormItem label="盘点任务ID" v-bind="validateInfos.taskId">
        <Input
          v-model:value="formData.taskId"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="资产ID" v-bind="validateInfos.assetId">
        <Input
          v-model:value="formData.assetId"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="物联二维码" v-bind="validateInfos.qrCode">
        <Input
          v-model:value="formData.qrCode"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="固定资产编码" v-bind="validateInfos.assetCode">
        <Input
          v-model:value="formData.assetCode"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="资产名称" v-bind="validateInfos.assetName">
        <Input
          v-model:value="formData.assetName"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="使用科室" v-bind="validateInfos.useDept">
        <Input
          v-model:value="formData.useDept"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="使用地址" v-bind="validateInfos.useLocal">
        <Input
          v-model:value="formData.useLocal"
          :placeholder="$t('ui.formRules.required')"
        />
      </FormItem>
      <FormItem label="盘点状态" v-bind="validateInfos.inventoryStatus">
        <Select
          v-model:value="formData.inventoryStatus"
          :options="getDictOptions('sys_yes_no')"
          :get-popup-container="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="备注" v-bind="validateInfos.remark">
        <Textarea
          v-model:value="formData.remark"
          :placeholder="$t('ui.formRules.required')"
          :rows="4"
        />
      </FormItem>
    </Form>
  </BasicModal>
</template>
