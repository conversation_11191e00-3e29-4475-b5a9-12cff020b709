import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDictTags } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '公司名称',
  },
  {
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      optionFilterProp: 'label',
      options: getDictOptions(DictEnum.COMPANY_TYPE),
    },
    fieldName: 'companyType',
    label: '公司类型',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'supplierId',
  },
  {
    title: '供应商编码',
    field: 'supplierCode',
  },
  {
    title: '公司名称',
    field: 'name',
  },
  {
    title: '公司类型',
    field: 'companyType',
    slots: {
      default: ({ row }) => {
        if (!row.companyType) {
          return '无';
        }
        const companyTypes = row.companyType
          .split(',') // 分割字符串为数组
          .map((type: string) => type.trim()) // 去除两端空格
          .filter(Boolean); // 过滤空字符串
        return renderDictTags(
          companyTypes,
          getDictOptions(DictEnum.COMPANY_TYPE),
          true,
          1,
        );
      },
    },
  },
  {
    title: '统一社会信用代码',
    field: 'creditCode',
  },
  {
    title: '银行账户',
    field: 'bankAccount',
  },
  {
    title: '开户行全称',
    field: 'bankName',
  },
  {
    title: '固定电话',
    field: 'companyPhone',
  },
  {
    title: '地址',
    field: 'companyAddress',
  },
  {
    title: '官方邮箱',
    field: 'officialEmail',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'supplierId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '供应商编码',
    fieldName: 'supplierCode',
    component: 'Input',
  },
  {
    label: '公司名称',
    fieldName: 'name',
    component: 'Input',
  },
  {
    label: '公司类型',
    fieldName: 'companyType',
    component: 'Select',
    componentProps: {
      getPopupContainer,
      mode: 'multiple',
      showSearch: true, // 启用搜索功能
      maxTagCount: 3, // 控制最多显示几个标签
      optionFilterProp: 'label',
      options: getDictOptions(DictEnum.COMPANY_TYPE) || [], // 防止 undefined 导致错误
      placeholder: '请选择公司类型', // 添加占位符提示
    },
  },
  {
    label: '统一社会信用代码',
    fieldName: 'creditCode',
    component: 'Input',
  },
  {
    label: '银行账户',
    fieldName: 'bankAccount',
    component: 'Input',
  },
  {
    label: '开户行全称',
    fieldName: 'bankName',
    component: 'Input',
  },
  {
    label: '固定电话',
    fieldName: 'companyPhone',
    component: 'Input',
  },
  {
    label: '地址',
    fieldName: 'companyAddress',
    component: 'Input',
  },
  {
    label: '官方邮箱',
    fieldName: 'officialEmail',
    component: 'Input',
  },
];
