<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { InventoryTaskForm } from '#/api/asset/inventoryTask/model';

import { Page, useVbenModal } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  inventoryTaskChangeStatus,
  inventoryTaskExport,
  inventoryTaskList,
  inventoryTaskRemove,
} from '#/api/asset/inventoryTask';
import { router } from '#/router';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './data';
import inventoryTaskModal from './inventoryTask-modal.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  fieldMappingTime: [
    [
      'createTime',
      ['params[beginCreateTime]', 'params[endCreateTime]'],
      ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
    ],
  ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await inventoryTaskList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'taskId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'asset-inventoryTask-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    cellClick: (e) => {
      const { row } = e;
      handleIntoryDetail(row, false);
    },
  },
});

const [InventoryTaskModal, modalApi] = useVbenModal({
  connectedComponent: inventoryTaskModal,
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(row: Required<InventoryTaskForm>) {
  modalApi.setData({ id: row.taskId });
  modalApi.open();
}

async function handleDelete(row: Required<InventoryTaskForm>) {
  await inventoryTaskRemove(Number(row.taskId));
  await tableApi.query();
}

// 修改盘点任务状态
async function handleUpdateStatus(taskId: string, inventoryStatus: number) {
  const data: InventoryTaskForm = {
    taskId,
    inventoryStatus,
  };
  try {
    await inventoryTaskChangeStatus(data);
    await tableApi.query();
  } catch {}
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<InventoryTaskForm>) =>
    row.taskId.toString(),
  );
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await inventoryTaskRemove(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(
    inventoryTaskExport,
    '资产盘点任务数据',
    tableApi.formApi.form.values,
    {
      fieldMappingTime: formOptions.fieldMappingTime,
    },
  );
}

/**
 * 配置分类数据
 * @param row
 * @param disabled  true为预览，false为配置
 */
function handleIntoryDetail(row: any, disabled: boolean) {
  router.push({
    path: '/asset/inventoryDetail',
    query: { taskId: row.taskId, disabled: String(disabled) },
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="资产盘点任务列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['asset:inventoryTask:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['asset:inventoryTask:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['asset:inventoryTask:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space style="display: flex; flex-direction: row; align-items: center">
          <ghost-button
            v-access:code="['asset:inventoryTask:edit']"
            @click.stop="handleUpdateStatus(row.taskId, 1)"
            v-if="row.inventoryStatus === 0"
          >
            {{ $t('pages.common.execute') }}
          </ghost-button>
          <ghost-button
            v-access:code="['asset:inventoryTask:edit']"
            @click.stop="handleUpdateStatus(row.taskId, 2)"
            v-if="row.inventoryStatus === 1"
          >
            {{ $t('pages.common.complete') }}
          </ghost-button>
          <ghost-button
            v-access:code="['asset:inventoryTask:edit']"
            @click.stop="handleUpdateStatus(row.taskId, 3)"
            v-if="row.inventoryStatus === 0"
          >
            {{ $t('pages.common.cancel') }}
          </ghost-button>
          <ghost-button
            v-access:code="['asset:inventoryTask:edit']"
            @click.stop="handleEdit(row)"
            v-if="row.inventoryStatus === 0"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
            @cancel.stop=""
          >
            <ghost-button
              danger
              v-access:code="['asset:inventoryTask:remove']"
              @click.stop=""
              v-if="row.inventoryStatus === 0 || row.inventoryStatus === 3"
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <InventoryTaskModal @reload="tableApi.query()" />
  </Page>
</template>
