import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted } from 'vue';

import { DictEnum } from '@vben/constants';

import { deptDict } from '#/api/system/dept';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export default {
  setup() {
    // 在组件挂载时主动触发加载
    onMounted(async () => {
      await deptDict.value; // 确保字典数据已加载完成
    });

    return {};
  },
};
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'assetName',
    label: '关键字',
    componentProps: {
      placeholder: '物联二维码、固定资产编码或资产名称',
    },
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_YES_NO 便于维护
      options: getDictOptions(DictEnum.AST_INVENTORY_STATUS),
    },
    fieldName: 'inventoryStatus',
    label: '盘点状态',
  },
  {
    component: 'TreeSelect',
    fieldName: 'useDept',
    label: '使用科室',
    componentProps: {
      fieldNames: { label: 'deptName', value: 'deptId' },
      showSearch: true,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      // 选中后显示在输入框的值
      treeNodeLabelProp: 'fullName',
      // 筛选的字段
      treeNodeFilterProp: 'deptName',
    },
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '物联二维码',
    field: 'qrCode',
  },
  {
    title: '固定资产编码',
    field: 'assetCode',
  },
  {
    title: '资产名称',
    field: 'assetName',
  },
  {
    title: '使用科室',
    field: 'useDept',
    formatter: ({ cellValue }) => {
      if (!cellValue) return '';
      const dict = deptDict.value;

      if (dict instanceof Map) {
        return dict.get(Number(cellValue)) || cellValue;
      } else if (dict && typeof dict === 'object') {
        return dict[cellValue] || cellValue;
      }
    },
  },
  {
    title: '地理位置',
    field: 'useLocal',
  },
  {
    title: '盘点状态',
    field: 'inventoryStatus',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.SYS_YES_NO 便于维护
        return renderDict(row.inventoryStatus, DictEnum.AST_INVENTORY_STATUS);
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 260,
  },
];
